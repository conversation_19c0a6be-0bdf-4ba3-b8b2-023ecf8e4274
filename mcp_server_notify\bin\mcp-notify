#!/usr/bin/env node

const io = require('socket.io-client');
const socket = io('http://localhost:3000');

// 解析命令行参数
const args = process.argv.slice(2);
const title = args[0] || 'MCP通知';
const message = args[1] || '任务已完成';

// 连接到服务器并发送通知
socket.on('connect', () => {
    console.log('已连接到MCP通知服务器');
    socket.emit('notify', {
        title,
        message,
        sound: true
    });
    console.log('通知已发送');
    process.exit(0);
});

socket.on('connect_error', (error) => {
    console.error('连接服务器失败:', error.message);
    process.exit(1);
});

// 设置超时
setTimeout(() => {
    console.error('连接超时');
    process.exit(1);
}, 5000); 
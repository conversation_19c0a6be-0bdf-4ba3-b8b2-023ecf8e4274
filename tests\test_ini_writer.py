import unittest
import os
import tempfile
import configparser
from typing import List, Dict, Any
from output_manager.ini_writer import INIWriter

class MockConfig:
    def __init__(self, output_dir: str):
        self.OUTPUT_DIR = output_dir

class TestINIWriter(unittest.TestCase):
    def setUp(self):
        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()
        self.config = MockConfig(self.temp_dir)
        self.writer = INIWriter(self.config)
        
        # 准备测试题目数据
        self.sample_questions = [
            {
                "type": "单选题",
                "question": "下面哪个是Python的关键字?",
                "options": "class|def|import|return",
                "answer": "A",
                "difficulty": "简单",
                "explanation": "class是Python的关键字",
                "knowledge_points": "Python基础|关键字"
            },
            {
                "type": "多选题",
                "question": "Python的数据类型包括?",
                "options": "int|str|list|dict",
                "answer": "A,B,C,D",
                "difficulty": "简单",
                "explanation": "这些都是Python的基础数据类型",
                "knowledge_points": "Python基础|数据类型"
            }
        ]

    def tearDown(self):
        # 清理临时文件
        for file in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, file))
        os.rmdir(self.temp_dir)

    def test_save_questions_to_ini(self):
        """测试基本保存功能"""
        file_path = self.writer.save_questions_to_ini(self.sample_questions)
        
        # 验证文件存在
        self.assertTrue(os.path.exists(file_path))
        
        # 验证文件内容格式
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        
        # 验证题目数量
        questions = [s for s in config.sections() if s.split('.')[0].isdigit()]
        self.assertEqual(len(questions), len(self.sample_questions))

    def test_append_questions(self):
        """测试增量保存功能"""
        # 第一次保存
        file_path = self.writer.append_questions(self.sample_questions[:1])
        
        # 第二次追加保存
        new_file_path = self.writer.append_questions(
            self.sample_questions[1:],
            filename=os.path.basename(file_path)
        )
        
        # 两次保存应该是同一文件
        self.assertEqual(file_path, new_file_path)
        
        # 验证总题目数
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        questions = [s for s in config.sections() if s.split('.')[0].isdigit()]
        self.assertEqual(len(questions), len(self.sample_questions))
        
        # 验证题目序号递增正确
        first_num = int(questions[0].split('.')[0])
        second_num = int(questions[1].split('.')[0])
        self.assertEqual(second_num, first_num + 1)

    def test_question_formatting(self):
        """测试各种题型格式化"""
        file_path = self.writer.save_questions_to_ini(self.sample_questions)
        config = configparser.ConfigParser()
        config.read(file_path, encoding='utf-8')
        
        # 验证单选题格式
        single_choice = config["1.【单选题】下面哪个是Python的关键字?"]
        self.assertEqual(single_choice['正确答案'], 'A')
        self.assertEqual(single_choice['题目难度'], '简单')
        
        # 验证多选题格式
        multi_choice = config["2.【多选题】Python的数据类型包括?"]
        self.assertEqual(multi_choice['正确答案'], 'A,B,C,D')

if __name__ == '__main__':
    unittest.main()
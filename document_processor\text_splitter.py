"""
智能文本分割模块
按照文档结构进行分割，保持语义完整性
"""

import re
import logging
from typing import List, Dict, Tuple
import tiktoken

logger = logging.getLogger(__name__)

class TextSplitter:
    """智能文本分割器"""

    def __init__(self, max_chunk_size: int = 5000, encoding_name: str = "cl100k_base",
                 disable_splitting: bool = False, enable_chunk_merging: bool = True,
                 use_new_splitting_logic: bool = False, min_chunk_size: int = 200):
        self.max_chunk_size = max_chunk_size
        self.disable_splitting = disable_splitting
        self.enable_chunk_merging = enable_chunk_merging
        self.use_new_splitting_logic = use_new_splitting_logic
        self.min_chunk_size = min_chunk_size
        self.encoding = tiktoken.get_encoding(encoding_name)

        if disable_splitting:
            logger.info("文档分割已禁用，将对整个文档生成题目")
        elif use_new_splitting_logic:
            logger.info(f"初始化文本分割器(新逻辑): max_chunk_size={max_chunk_size}, 无重叠, enable_chunk_merging={enable_chunk_merging}")
        else:
            logger.info(f"初始化文本分割器: max_chunk_size={max_chunk_size}, enable_chunk_merging={enable_chunk_merging}")

    def count_tokens(self, text: str) -> int:
        """计算文本的token数量"""
        return len(self.encoding.encode(text))

    def count_characters(self, text: str) -> int:
        """计算文本的字符数量"""
        return len(text.strip())

    def split_document(self, doc_info: Dict[str, any]) -> List[Dict[str, any]]:
        """
        分割文档内容

        Args:
            doc_info: 文档信息字典

        Returns:
            分割后的文本块列表
        """
        content = doc_info['content']
        structure = doc_info.get('structure', [])
        filename = doc_info['filename']

        # 如果禁用分割，直接返回整个文档作为单个块
        if self.disable_splitting:
            logger.info(f"文档分割已禁用，文档 {filename} 作为单个块处理")
            return [{
                'filename': filename,
                'chunk_index': 0,
                'content': content,
                'token_count': self.count_tokens(content),
                'char_count': self.count_characters(content),
                'structure_info': structure
            }]

        # 如果使用新的分块逻辑
        if self.use_new_splitting_logic:
            return self._split_document_new_logic(content, structure, filename)

        # 如果文档较短，直接返回
        if self.count_tokens(content) <= self.max_chunk_size:
            return [{
                'filename': filename,
                'chunk_index': 0,
                'content': content,
                'token_count': self.count_tokens(content),
                'structure_info': structure
            }]

        # 按结构分割
        chunks = self._split_by_structure(content, structure, filename)

        # 如果结构分割后仍有过长的块，进一步分割
        final_chunks = []
        for chunk in chunks:
            if chunk['token_count'] > self.max_chunk_size:
                sub_chunks = self._split_long_chunk(chunk)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)

        # 重新编号
        for i, chunk in enumerate(final_chunks):
            chunk['chunk_index'] = i
            # 添加字符数统计
            chunk['char_count'] = self.count_characters(chunk['content'])

        logger.info(f"文档 {filename} 分割为 {len(final_chunks)} 个块")
        return final_chunks

    def _split_by_structure(self, content: str, structure: List[Dict], filename: str) -> List[Dict[str, any]]:
        """按文档结构分割"""
        chunks = []
        current_chunk = ""
        current_structure = []

        # 如果没有结构信息，按段落分割
        if not structure:
            paragraphs = content.split('\n\n')
            return self._split_by_paragraphs(paragraphs, filename)

        for item in structure:
            if item['type'] == 'paragraph':
                text = item['text']

                # 如果是标题，可能需要开始新的块
                if item.get('is_heading', False) and current_chunk:
                    # 保存当前块
                    if current_chunk.strip():
                        chunks.append({
                            'filename': filename,
                            'chunk_index': len(chunks),
                            'content': current_chunk.strip(),
                            'token_count': self.count_tokens(current_chunk),
                            'structure_info': current_structure.copy()
                        })

                    # 开始新块
                    current_chunk = text + '\n\n'
                    current_structure = [item]
                else:
                    # 检查添加这段文本后是否超过限制
                    test_chunk = current_chunk + text + '\n\n'
                    if self.count_tokens(test_chunk) > self.max_chunk_size and current_chunk:
                        # 保存当前块
                        chunks.append({
                            'filename': filename,
                            'chunk_index': len(chunks),
                            'content': current_chunk.strip(),
                            'token_count': self.count_tokens(current_chunk),
                            'structure_info': current_structure.copy()
                        })

                        # 开始新块，保留重叠内容
                        overlap_text = self._get_overlap_text(current_chunk)
                        current_chunk = overlap_text + text + '\n\n'
                        current_structure = [item]
                    else:
                        current_chunk += text + '\n\n'
                        current_structure.append(item)

            elif item['type'] == 'table':
                table_text = self._format_table_content(item['content'])

                # 表格通常作为独立块处理
                if current_chunk.strip():
                    chunks.append({
                        'filename': filename,
                        'chunk_index': len(chunks),
                        'content': current_chunk.strip(),
                        'token_count': self.count_tokens(current_chunk),
                        'structure_info': current_structure.copy()
                    })
                    current_chunk = ""
                    current_structure = []

                chunks.append({
                    'filename': filename,
                    'chunk_index': len(chunks),
                    'content': table_text,
                    'token_count': self.count_tokens(table_text),
                    'structure_info': [item]
                })

        # 处理最后一个块
        if current_chunk.strip():
            chunks.append({
                'filename': filename,
                'chunk_index': len(chunks),
                'content': current_chunk.strip(),
                'token_count': self.count_tokens(current_chunk),
                'structure_info': current_structure
            })

        return chunks

    def _split_by_paragraphs(self, paragraphs: List[str], filename: str) -> List[Dict[str, any]]:
        """按段落分割文本"""
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            if not paragraph.strip():
                continue

            test_chunk = current_chunk + paragraph + '\n\n'
            if self.count_tokens(test_chunk) > self.max_chunk_size and current_chunk:
                # 保存当前块
                chunks.append({
                    'filename': filename,
                    'chunk_index': len(chunks),
                    'content': current_chunk.strip(),
                    'token_count': self.count_tokens(current_chunk),
                    'structure_info': []
                })

                # 开始新块
                overlap_text = self._get_overlap_text(current_chunk)
                current_chunk = overlap_text + paragraph + '\n\n'
            else:
                current_chunk += paragraph + '\n\n'

        # 处理最后一个块
        if current_chunk.strip():
            chunks.append({
                'filename': filename,
                'chunk_index': len(chunks),
                'content': current_chunk.strip(),
                'token_count': self.count_tokens(current_chunk),
                'structure_info': []
            })

        return chunks

    def _split_long_chunk(self, chunk: Dict[str, any]) -> List[Dict[str, any]]:
        """分割过长的文本块"""
        content = chunk['content']
        sentences = re.split(r'[.!?。！？]\s+', content)

        sub_chunks = []
        current_text = ""

        for sentence in sentences:
            if not sentence.strip():
                continue

            test_text = current_text + sentence + '. '
            if self.count_tokens(test_text) > self.max_chunk_size and current_text:
                # 保存当前子块
                sub_chunks.append({
                    'filename': chunk['filename'],
                    'chunk_index': chunk['chunk_index'],
                    'content': current_text.strip(),
                    'token_count': self.count_tokens(current_text),
                    'structure_info': chunk['structure_info']
                })

                # 开始新子块
                overlap_text = self._get_overlap_text(current_text)
                current_text = overlap_text + sentence + '. '
            else:
                current_text += sentence + '. '

        # 处理最后一个子块
        if current_text.strip():
            sub_chunks.append({
                'filename': chunk['filename'],
                'chunk_index': chunk['chunk_index'],
                'content': current_text.strip(),
                'token_count': self.count_tokens(current_text),
                'structure_info': chunk['structure_info']
            })

        return sub_chunks

    def _get_overlap_text(self, text: str) -> str:
        """获取重叠文本，保持上下文连贯（已禁用重叠功能）"""
        return ""

    def _format_table_content(self, table_data: List[List[str]]) -> str:
        """格式化表格内容"""
        if not table_data:
            return ""

        formatted_rows = []
        for row in table_data:
            formatted_rows.append(' | '.join(row))

        return '\n'.join(formatted_rows)

    def batch_split_documents(self, documents: List[Dict[str, any]]) -> List[Dict[str, any]]:
        """批量分割文档"""
        all_chunks = []

        for doc in documents:
            try:
                chunks = self.split_document(doc)
                all_chunks.extend(chunks)
            except Exception as e:
                logger.error(f"分割文档失败 {doc.get('filename', 'unknown')}: {str(e)}")
                continue

        logger.info(f"批量分割完成，共生成 {len(all_chunks)} 个文本块")
        return all_chunks

    def try_merge_with_next_chunks(self, chunks: List[Dict[str, any]],
                                   current_index: int) -> Tuple[Dict[str, any], int]:
        """
        尝试将当前分块与后续分块合并，直到字符数超过最大分块大小

        Args:
            chunks: 所有分块列表
            current_index: 当前分块索引

        Returns:
            (合并后的分块, 下一个要处理的索引)
        """
        if current_index >= len(chunks):
            return None, current_index

        current_chunk = chunks[current_index]
        merged_content = current_chunk['content']
        merged_structure = current_chunk.get('structure_info', []).copy()
        char_count = self.count_characters(merged_content)

        # 如果当前分块已经超过最大分块大小，直接返回
        if char_count >= self.max_chunk_size:
            return current_chunk, current_index + 1

        next_index = current_index + 1

        # 尝试与后续分块合并
        while next_index < len(chunks) and char_count < self.max_chunk_size:
            next_chunk = chunks[next_index]
            test_content = merged_content + '\n\n' + next_chunk['content']
            test_char_count = self.count_characters(test_content)

            # 如果合并后超过最大分块大小，停止合并
            if test_char_count > self.max_chunk_size:
                break

            # 执行合并
            merged_content = test_content
            merged_structure.extend(next_chunk.get('structure_info', []))
            char_count = test_char_count
            next_index += 1

        # 创建合并后的分块
        merged_chunk = {
            'filename': current_chunk['filename'],
            'chunk_index': current_chunk['chunk_index'],
            'content': merged_content,
            'token_count': self.count_tokens(merged_content),
            'char_count': char_count,
            'structure_info': merged_structure,
            'merged_from': list(range(current_index, next_index))  # 记录合并来源
        }

        logger.info(f"分块合并: 索引 {current_index}-{next_index-1}, "
                   f"字符数: {char_count}, 来源文件: {current_chunk['filename']}")

        return merged_chunk, next_index

    def _split_document_new_logic(self, content: str, structure: List[Dict], filename: str) -> List[Dict[str, any]]:
        """
        新的文档分块逻辑：
        1. 统计文档字数
        2. 根据字数/最大文本块大小计算分段数
        3. 按结构划分并合并到满足最小字数要求
        """
        char_count = self.count_characters(content)
        logger.info(f"文档 {filename} 字符数: {char_count}")

        # 计算分段数
        if char_count <= self.max_chunk_size:
            segment_count = 1
        else:
            segment_count = (char_count + self.max_chunk_size - 1) // self.max_chunk_size  # 向上取整

        logger.info(f"文档 {filename} 计划分为 {segment_count} 段")

        # 如果只需要一段，直接返回整个文档
        if segment_count == 1:
            return [{
                'filename': filename,
                'chunk_index': 0,
                'content': content,
                'token_count': self.count_tokens(content),
                'char_count': char_count,
                'structure_info': structure
            }]

        # 计算每段的目标字符数
        target_chars_per_segment = char_count // segment_count
        min_chars_per_segment = int(target_chars_per_segment * 0.9)  # 至少90%的目标字符数

        logger.info(f"每段目标字符数: {target_chars_per_segment}, 最小字符数: {min_chars_per_segment}")

        # 按结构分割并合并
        chunks = self._split_by_structure_new_logic(content, structure, filename, segment_count, min_chars_per_segment)

        # 应用合并和二次分割逻辑
        chunks = self._merge_and_re_split_chunks(chunks, filename)

        logger.info(f"文档 {filename} 实际分为 {len(chunks)} 段")
        return chunks

    def _split_by_structure_new_logic(self, content: str, structure: List[Dict], filename: str,
                                    target_segments: int, min_chars_per_segment: int) -> List[Dict[str, any]]:
        """按新逻辑进行结构化分割"""
        chunks = []
        current_chunk = ""
        current_structure = []

        # 如果没有结构信息，按段落分割
        if not structure:
            paragraphs = content.split('\n\n')
            return self._split_paragraphs_new_logic(paragraphs, filename, target_segments, min_chars_per_segment)

        for item in structure:
            if item['type'] == 'paragraph':
                text = item['text']

                # 检查是否是标题，如果是标题且当前块已有内容，考虑是否开始新块
                if item.get('is_heading', False) and current_chunk:
                    current_char_count = self.count_characters(current_chunk)

                    # 如果当前块已达到最小字符数要求，开始新块
                    if current_char_count >= min_chars_per_segment:
                        chunks.append({
                            'filename': filename,
                            'chunk_index': len(chunks),
                            'content': current_chunk.strip(),
                            'token_count': self.count_tokens(current_chunk),
                            'structure_info': current_structure.copy()
                        })

                        # 开始新块（无重叠）
                        current_chunk = text + '\n\n'
                        current_structure = [item]
                    else:
                        # 当前块字符数不足，继续添加
                        current_chunk += text + '\n\n'
                        current_structure.append(item)
                else:
                    # 添加到当前块
                    current_chunk += text + '\n\n'
                    current_structure.append(item)

            elif item['type'] == 'table':
                table_text = self._format_table_content(item['content'])

                # 检查当前块是否已达到最小字符数
                current_char_count = self.count_characters(current_chunk)
                if current_char_count >= min_chars_per_segment and current_chunk.strip():
                    # 保存当前块
                    chunks.append({
                        'filename': filename,
                        'chunk_index': len(chunks),
                        'content': current_chunk.strip(),
                        'token_count': self.count_tokens(current_chunk),
                        'structure_info': current_structure.copy()
                    })
                    current_chunk = ""
                    current_structure = []

                # 表格作为独立内容添加到当前块
                current_chunk += table_text + '\n\n'
                current_structure.append(item)

        # 处理最后一个块
        if current_chunk.strip():
            chunks.append({
                'filename': filename,
                'chunk_index': len(chunks),
                'content': current_chunk.strip(),
                'token_count': self.count_tokens(current_chunk),
                'structure_info': current_structure
            })

        return chunks

    def _split_paragraphs_new_logic(self, paragraphs: List[str], filename: str,
                                  target_segments: int, min_chars_per_segment: int) -> List[Dict[str, any]]:
        """按新逻辑分割段落"""
        chunks = []
        current_chunk = ""

        # 如果段落很少，直接按字符数强制分割
        if len(paragraphs) < target_segments:
            # 合并所有段落
            all_content = '\n\n'.join(p for p in paragraphs if p.strip())
            return self._force_split_by_chars(all_content, filename, target_segments, min_chars_per_segment)

        for i, paragraph in enumerate(paragraphs):
            if not paragraph.strip():
                continue

            # 添加段落到当前块
            if current_chunk:
                current_chunk += '\n\n' + paragraph
            else:
                current_chunk = paragraph

            current_char_count = self.count_characters(current_chunk)

            # 检查是否应该结束当前块
            should_end_chunk = False

            # 如果已达到最小字符数且还有足够的段落分配给剩余的块
            if current_char_count >= min_chars_per_segment:
                remaining_paragraphs = len(paragraphs) - i - 1
                remaining_chunks_needed = target_segments - len(chunks) - 1

                # 如果剩余段落足够分配给剩余块，或者当前块已经很大了
                if remaining_paragraphs >= remaining_chunks_needed or current_char_count >= min_chars_per_segment * 1.5:
                    should_end_chunk = True

            if should_end_chunk and len(chunks) < target_segments - 1:
                # 保存当前块
                chunks.append({
                    'filename': filename,
                    'chunk_index': len(chunks),
                    'content': current_chunk.strip(),
                    'token_count': self.count_tokens(current_chunk),
                    'structure_info': []
                })
                current_chunk = ""

        # 处理最后一个块
        if current_chunk.strip():
            chunks.append({
                'filename': filename,
                'chunk_index': len(chunks),
                'content': current_chunk.strip(),
                'token_count': self.count_tokens(current_chunk),
                'structure_info': []
            })

        return chunks

    def _merge_and_re_split_chunks(self, chunks: List[Dict[str, any]], filename: str) -> List[Dict[str, any]]:
        """
        合并小于最小字符数的分块，并重新分割超过最大字符数的分块。
        """
        processed_chunks = []
        i = 0
        while i < len(chunks):
            current_chunk = chunks[i]
            current_content = current_chunk['content']
            current_char_count = self.count_characters(current_content)

            # 如果当前块小于最小字符数，尝试与前一个块合并
            if self.enable_chunk_merging and current_char_count < self.min_chunk_size and len(processed_chunks) > 0:
                last_processed_chunk = processed_chunks[-1]
                test_content = last_processed_chunk['content'] + '\n\n' + current_content
                test_char_count = self.count_characters(test_content)

                # 如果合并后不超过最大分块大小，则执行合并
                if test_char_count <= self.max_chunk_size:
                    merged_content = test_content
                    merged_structure = last_processed_chunk.get('structure_info', []) + current_chunk.get('structure_info', [])
                    
                    processed_chunks[-1] = {
                        'filename': current_chunk['filename'],
                        'content': merged_content,
                        'token_count': self.count_tokens(merged_content),
                        'char_count': self.count_characters(merged_content),
                        'structure_info': merged_structure
                    }
                    logger.info(f"分块合并（向前）: 索引 {len(processed_chunks)-1} 与 {i} 合并, "
                               f"字符数: {self.count_characters(merged_content)}, 来源文件: {current_chunk['filename']}")
                else:
                    # 如果合并后超过最大分块大小，不合并，将当前块作为新块处理
                    processed_chunks.append(current_chunk)
            else:
                processed_chunks.append(current_chunk)
            i += 1

        final_chunks = []
        for chunk in processed_chunks:
            char_count = self.count_characters(chunk['content'])
            if char_count > self.max_chunk_size:
                logger.warning(f"分块字符数 {char_count} 超过最大限制 {self.max_chunk_size}，进行二次分割。")
                sub_chunks = self._force_split_by_chars(chunk['content'], chunk['filename'],
                                                      (char_count + self.max_chunk_size - 1) // self.max_chunk_size,
                                                      self.min_chunk_size)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)

        # 重新编号
        for idx, chunk in enumerate(final_chunks):
            chunk['chunk_index'] = idx

        return final_chunks

    def _force_split_by_chars(self, content: str, filename: str, target_segments: int, min_chars_per_segment: int) -> List[Dict[str, any]]:
        """按字符数强制分割"""
        chunks = []
        char_count = len(content)
        chars_per_segment = char_count // target_segments

        start = 0
        for i in range(target_segments):
            if i == target_segments - 1:
                # 最后一段包含所有剩余内容
                chunk_content = content[start:]
            else:
                end = start + chars_per_segment
                # 尝试在句号或换行处分割
                while end < len(content) and content[end] not in '。！？\n':
                    end += 1
                chunk_content = content[start:end]
                start = end

            if chunk_content.strip():
                chunks.append({
                    'filename': filename,
                    'chunk_index': len(chunks),
                    'content': chunk_content.strip(),
                    'token_count': self.count_tokens(chunk_content),
                    'structure_info': []
                })

        return chunks

import unittest
from unittest.mock import patch, MagicMock
import os
import tempfile
from document_processor.doc_reader import DocumentReader
from output_manager.ini_writer import INIWriter
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from config import Config

class TestIntegration(unittest.TestCase):
    def setUp(self):
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 模拟配置
        self.config = Config()
        self.config.OUTPUT_DIR = self.temp_dir
        self.config.BATCH_SAVE_THRESHOLD = 2  # 每2题触发保存
        
        # 模拟LLM生成题目
        self.mock_questions = [
            {
                'type': '单选题',
                'question': '问题1',
                'options': 'A|B|C',
                'answer': 'A',
                'difficulty': '简单',
                'explanation': '解析1',
                'knowledge_points': '知识点1'
            },
            {
                'type': '多选题', 
                'question': '问题2',
                'options': 'A|B|C|D',
                'answer': 'A,B',
                'difficulty': '中等',
                'explanation': '解析2',
                'knowledge_points': '知识点2'
            }
        ]
        
    def tearDown(self):
        # 清理临时文件
        for f in os.listdir(self.temp_dir):
            os.remove(os.path.join(self.temp_dir, f))
        os.rmdir(self.temp_dir)
    
    @patch('llm_service.openai_client.OpenAICompatibleClient.generate_quiz')
    def test_full_processing_flow(self, mock_gen):
        """测试完整处理流程"""
        # 模拟LLM返回题目
        mock_gen.return_value = {'questions': self.mock_questions}
        
        # 创建处理器
        processor = DocumentToQuizProcessor(self.config)
        
        # 模拟文档读取结果
        mock_doc = {
            'filename': 'test.docx',
            'content': '文档内容',
            'structure': [],
            'metadata': {}
        }
        
        with patch.object(DocumentReader, 'read_document', return_value=mock_doc):
            # 处理文档
            result = processor.process_documents(self.temp_dir, recursive=False)
            
            # 验证结果
            self.assertEqual(result['total_questions'], len(self.mock_questions))
            
            # 验证INI文件生成
            ini_files = [f for f in os.listdir(self.temp_dir) if f.endswith('.ini')]
            self.assertEqual(len(ini_files), 1)
            
            # 验证INI文件内容
            with open(os.path.join(self.temp_dir, ini_files[0]), 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('问题1', content)
                self.assertIn('问题2', content)

    def test_incremental_saving(self):
        """测试增量保存机制"""
        # 创建INI写入器
        writer = INIWriter(self.config)
        
        # 第一次保存
        path1 = writer.append_questions(self.mock_questions[:1])
        
        # 第二次保存(增量)
        path2 = writer.append_questions(self.mock_questions[1:], 
                                      os.path.basename(path1))
        
        # 应是同一文件
        self.assertEqual(path1, path2)
        
        # 验证总题目数
        with open(path1, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertEqual(content.count('【单选题】'), 1)
            self.assertEqual(content.count('【多选题】'), 1)

if __name__ == '__main__':
    unittest.main()
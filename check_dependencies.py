#!/usr/bin/env python3
"""
依赖检查脚本
检查项目所需依赖的安装状态和版本
"""

import sys
import importlib
import subprocess
import logging
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# 依赖配置
DEPENDENCIES = {
    'core': {
        'openai': {
            'min_version': '1.0.0',
            'description': 'OpenAI API客户端',
            'import_name': 'openai',
            'required': True
        },
        'python-docx': {
            'min_version': '0.8.11',
            'description': 'DOCX文件处理',
            'import_name': 'docx',
            'required': True
        },
        'pandas': {
            'min_version': '1.3.0',
            'description': 'CSV数据处理',
            'import_name': 'pandas',
            'required': True
        },
        'tiktoken': {
            'min_version': '0.5.0',
            'description': 'Token计数',
            'import_name': 'tiktoken',
            'required': True
        },
        'requests': {
            'min_version': '2.25.0',
            'description': 'HTTP请求',
            'import_name': 'requests',
            'required': True
        },
        'pdfplumber': {
            'min_version': '0.7.0',
            'description': 'PDF文件处理',
            'import_name': 'pdfplumber',
            'required': True
        }
    },
    'optional': {
        'textract': {
            'min_version': '1.6.1',
            'description': '统一文档处理（DOC/DOCX/PDF）',
            'import_name': 'textract',
            'required': False
        },
        'customtkinter': {
            'min_version': '5.0.0',
            'description': '现代化GUI界面',
            'import_name': 'customtkinter',
            'required': False
        },
        'Pillow': {
            'min_version': '8.0.0',
            'description': '图像处理（GUI需要）',
            'import_name': 'PIL',
            'required': False
        }
    }
}

def get_package_version(package_name: str) -> str:
    """获取包版本"""
    try:
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'show', package_name],
            capture_output=True, text=True, check=True
        )
        for line in result.stdout.split('\n'):
            if line.startswith('Version:'):
                return line.split(':', 1)[1].strip()
    except subprocess.CalledProcessError:
        pass
    return None

def check_import(import_name: str) -> bool:
    """检查模块是否可以导入"""
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def compare_versions(version1: str, version2: str) -> int:
    """比较版本号，返回 -1, 0, 1"""
    try:
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]
        
        # 补齐长度
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))
        
        for v1, v2 in zip(v1_parts, v2_parts):
            if v1 < v2:
                return -1
            elif v1 > v2:
                return 1
        return 0
    except ValueError:
        return 0

def check_dependency(package_name: str, config: Dict) -> Dict:
    """检查单个依赖"""
    result = {
        'package': package_name,
        'description': config['description'],
        'required': config['required'],
        'installed': False,
        'importable': False,
        'version': None,
        'min_version': config['min_version'],
        'version_ok': False,
        'status': 'missing'
    }
    
    # 检查是否安装
    version = get_package_version(package_name)
    if version:
        result['installed'] = True
        result['version'] = version
        
        # 检查版本
        if compare_versions(version, config['min_version']) >= 0:
            result['version_ok'] = True
        
    # 检查是否可导入
    if check_import(config['import_name']):
        result['importable'] = True
    
    # 确定状态
    if result['installed'] and result['importable'] and result['version_ok']:
        result['status'] = 'ok'
    elif result['installed'] and result['importable']:
        result['status'] = 'version_low'
    elif result['installed']:
        result['status'] = 'import_error'
    else:
        result['status'] = 'missing'
    
    return result

def check_all_dependencies() -> Tuple[List[Dict], List[Dict]]:
    """检查所有依赖"""
    core_results = []
    optional_results = []
    
    logger.info("=== 检查核心依赖 ===")
    for package, config in DEPENDENCIES['core'].items():
        result = check_dependency(package, config)
        core_results.append(result)
        
        status_icon = {
            'ok': '✓',
            'version_low': '⚠',
            'import_error': '✗',
            'missing': '✗'
        }.get(result['status'], '?')
        
        logger.info(f"{status_icon} {package}: {result['status']}")
        if result['version']:
            logger.info(f"    版本: {result['version']} (需要: >={result['min_version']})")
    
    logger.info("\n=== 检查可选依赖 ===")
    for package, config in DEPENDENCIES['optional'].items():
        result = check_dependency(package, config)
        optional_results.append(result)
        
        status_icon = {
            'ok': '✓',
            'version_low': '⚠',
            'import_error': '✗',
            'missing': '-'
        }.get(result['status'], '?')
        
        logger.info(f"{status_icon} {package}: {result['status']}")
        if result['version']:
            logger.info(f"    版本: {result['version']} (需要: >={result['min_version']})")
    
    return core_results, optional_results

def generate_report(core_results: List[Dict], optional_results: List[Dict]) -> Dict:
    """生成检查报告"""
    report = {
        'core_dependencies': {
            'total': len(core_results),
            'ok': 0,
            'issues': 0,
            'missing': 0
        },
        'optional_dependencies': {
            'total': len(optional_results),
            'ok': 0,
            'issues': 0,
            'missing': 0
        },
        'issues': [],
        'missing_packages': [],
        'upgrade_needed': []
    }
    
    # 分析核心依赖
    for result in core_results:
        if result['status'] == 'ok':
            report['core_dependencies']['ok'] += 1
        elif result['status'] == 'missing':
            report['core_dependencies']['missing'] += 1
            report['missing_packages'].append(result['package'])
        else:
            report['core_dependencies']['issues'] += 1
            report['issues'].append(result)
            
        if result['status'] == 'version_low':
            report['upgrade_needed'].append(result['package'])
    
    # 分析可选依赖
    for result in optional_results:
        if result['status'] == 'ok':
            report['optional_dependencies']['ok'] += 1
        elif result['status'] == 'missing':
            report['optional_dependencies']['missing'] += 1
        else:
            report['optional_dependencies']['issues'] += 1
            
        if result['status'] == 'version_low':
            report['upgrade_needed'].append(result['package'])
    
    return report

def print_summary(report: Dict):
    """打印摘要"""
    logger.info("\n=== 依赖检查摘要 ===")
    
    core = report['core_dependencies']
    optional = report['optional_dependencies']
    
    logger.info(f"核心依赖: {core['ok']}/{core['total']} 正常")
    if core['missing'] > 0:
        logger.error(f"  缺失: {core['missing']} 个")
    if core['issues'] > 0:
        logger.warning(f"  问题: {core['issues']} 个")
    
    logger.info(f"可选依赖: {optional['ok']}/{optional['total']} 正常")
    if optional['missing'] > 0:
        logger.info(f"  缺失: {optional['missing']} 个")
    if optional['issues'] > 0:
        logger.warning(f"  问题: {optional['issues']} 个")
    
    # 提供解决建议
    if report['missing_packages']:
        logger.info(f"\n需要安装的包:")
        for package in report['missing_packages']:
            logger.info(f"  pip install {package}")
    
    if report['upgrade_needed']:
        logger.info(f"\n需要升级的包:")
        for package in report['upgrade_needed']:
            logger.info(f"  pip install --upgrade {package}")
    
    # 总体状态
    if core['ok'] == core['total']:
        logger.info("\n🎉 所有核心依赖都正常！")
        return True
    else:
        logger.error(f"\n❌ 有 {core['total'] - core['ok']} 个核心依赖存在问题")
        return False

def main():
    """主函数"""
    logger.info("=== 项目依赖检查 ===")
    logger.info(f"Python版本: {sys.version}")
    
    # 检查所有依赖
    core_results, optional_results = check_all_dependencies()
    
    # 生成报告
    report = generate_report(core_results, optional_results)
    
    # 打印摘要
    success = print_summary(report)
    
    if success:
        logger.info("\n可以正常运行项目！")
        return 0
    else:
        logger.info("\n请先解决依赖问题，然后重新检查")
        logger.info("运行 python install_dependencies.py 自动安装依赖")
        return 1

if __name__ == "__main__":
    sys.exit(main())

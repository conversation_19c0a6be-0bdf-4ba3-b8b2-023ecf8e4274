#!/usr/bin/env python3
"""
配置设置脚本
帮助用户快速配置文档到题库工具
"""

import os
import sys
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config

def main():
    """主函数"""
    print("欢迎使用文档到题库工具配置向导！")
    print()
    
    # 检查是否已有配置文件
    existing_configs = []
    for filename in ['config.json', 'config.ini']:
        if os.path.exists(filename):
            existing_configs.append(filename)
    
    if existing_configs:
        print(f"检测到现有配置文件: {', '.join(existing_configs)}")
        choice = input("是否要重新配置？(y/n): ").strip().lower()
        if choice != 'y':
            print("配置取消。")
            return 0
    
    try:
        # 运行配置向导
        config = Config.setup_wizard()
        
        print("\n配置完成！")
        print("\n下一步:")
        print("1. 准备要处理的DOC/DOCX文档")
        print("2. 运行GUI: python gui_app.py")
        print("3. 或运行命令行: python main.py --input_dir ./documents")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n配置被用户取消。")
        return 1
    except Exception as e:
        print(f"\n配置过程中出现错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

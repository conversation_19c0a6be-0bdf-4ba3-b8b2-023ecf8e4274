"""
分析失败片段为什么生成失败
"""

import os
import sys
import json
import requests
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def read_failed_chunk():
    """读取失败片段内容"""
    file_path = "err-docs/failed_realtime_附件5：660MW超临界机组输煤运行规程（2024修订）.docx_chunk5_20250619_151730_475.txt"
    
    if not os.path.exists(file_path):
        print(f"[错误] 文件不存在: {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取文档内容部分
        lines = content.split('\n')
        content_start = False
        document_content = []
        
        for line in lines:
            if line.strip() == "=== 文档内容 ===":
                content_start = True
                continue
            if content_start:
                document_content.append(line)
        
        return '\n'.join(document_content)
        
    except Exception as e:
        print(f"[错误] 读取文件失败: {str(e)}")
        return None

def analyze_content_characteristics(content):
    """分析内容特征"""
    print("=" * 60)
    print("内容特征分析")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供分析")
        return
    
    # 基本统计
    char_count = len(content)
    word_count = len(content.split())
    line_count = len(content.split('\n'))
    sentence_count = content.count('。') + content.count('.')
    
    print(f"[基本统计]:")
    print(f"  字符数: {char_count}")
    print(f"  词汇数: {word_count}")
    print(f"  行数: {line_count}")
    print(f"  句子数: {sentence_count}")
    
    # 内容结构分析
    print(f"\n[内容结构分析]:")
    
    # 检查是否包含表格引用
    table_refs = content.count('表')
    print(f"  表格引用: {table_refs} 个")
    
    # 检查是否包含条款结构
    if '第' in content and ('条' in content or '章' in content):
        print(f"  [OK] 包含条款结构")
    else:
        print(f"  [警告] 缺少明显的条款结构")
    
    # 检查内容类型
    content_types = []
    if '注意事项' in content:
        content_types.append("注意事项")
    if '运行中' in content:
        content_types.append("运行指导")
    if '检查' in content:
        content_types.append("检查项目")
    if '维护' in content:
        content_types.append("维护工作")
    if '故障' in content:
        content_types.append("故障处理")
    if '概述' in content:
        content_types.append("设备概述")
    if '规范' in content or '参数' in content:
        content_types.append("技术规范")
    
    print(f"  内容类型: {', '.join(content_types) if content_types else '未识别'}")
    
    # 检查专业术语
    technical_terms = ['翻车机', '斗轮', '拨车机', '迁车台', '液压', '齿轮', '皮带', '输煤']
    found_terms = [term for term in technical_terms if term in content]
    print(f"  专业术语: {', '.join(found_terms[:5])}")
    
    # 检查规范性语言
    regulatory_keywords = ['应', '必须', '禁止', '不得', '严禁', '注意', '检查', '确认']
    found_regulatory = [kw for kw in regulatory_keywords if kw in content]
    print(f"  规范性语言: {', '.join(found_regulatory[:5])}")
    
    # 分析内容密度
    print(f"\n[内容密度分析]:")
    avg_sentence_length = char_count / max(sentence_count, 1)
    print(f"  平均句子长度: {avg_sentence_length:.1f} 字符")
    
    if avg_sentence_length > 50:
        print(f"  [OK] 句子较长，信息密度高")
    else:
        print(f"  [警告] 句子较短，可能信息密度不足")

def test_with_llm_directly(content):
    """直接用LLM测试这个内容"""
    print("\n" + "=" * 60)
    print("LLM直接测试")
    print("=" * 60)
    
    api_url = "http://10.45.131.70:3001/v1/chat/completions"
    api_key = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    model = "rsv-rcgjfz7v"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试不同的提示词策略
    test_cases = [
        {
            "name": "标准提示词",
            "prompt": f"""请根据以下文档内容生成题目，严格按照新模板.ini格式要求。要求生成以下数量的题目：
- 单选题：2道
- 多选题：1道
- 简答题：1道

**重要要求**：
1. 严格基于文档内容，不得编造信息
2. 如果内容不足以生成指定数量的题目，请返回"insufficient_content"

文档内容：
{content}"""
        },
        {
            "name": "简化要求",
            "prompt": f"""请根据以下文档内容生成1道单选题。

**要求**：
- 严格基于文档内容
- 如果内容不足，请返回"insufficient_content"

文档内容：
{content}"""
        },
        {
            "name": "专注操作规程",
            "prompt": f"""请根据以下输煤系统操作规程内容生成1道关于设备操作注意事项的单选题。

文档内容：
{content}"""
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[测试 {i}] {test_case['name']}:")
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的题目生成助手。请严格按照要求输出。"
                },
                {
                    "role": "user",
                    "content": test_case['prompt']
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(api_url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                response_json = response.json()
                content_response = response_json['choices'][0]['message']['content']
                
                print(f"  API响应长度: {len(content_response)} 字符")
                
                if "insufficient_content" in content_response.lower():
                    print(f"  [失败] LLM报告内容不足")
                    print(f"  响应: {content_response[:200]}...")
                elif "【单选题】" in content_response or "单选题" in content_response:
                    print(f"  [成功] LLM生成了题目")
                    print(f"  响应预览: {content_response[:300]}...")
                else:
                    print(f"  [异常] LLM响应格式异常")
                    print(f"  响应预览: {content_response[:200]}...")
            else:
                print(f"  [错误] API调用失败: {response.status_code}")
                
        except Exception as e:
            print(f"  [异常] API调用异常: {str(e)}")

def analyze_content_sections(content):
    """分析内容各个部分"""
    print("\n" + "=" * 60)
    print("内容分段分析")
    print("=" * 60)
    
    # 按空行分割内容
    sections = [section.strip() for section in content.split('\n\n') if section.strip()]
    
    print(f"[分段统计] 共 {len(sections)} 个段落")
    
    # 分析每个段落
    substantial_sections = []
    for i, section in enumerate(sections[:10], 1):  # 只分析前10个段落
        char_count = len(section)
        sentence_count = section.count('。') + section.count('.')
        
        print(f"\n  段落 {i}:")
        print(f"    字符数: {char_count}")
        print(f"    句子数: {sentence_count}")
        print(f"    内容: {section[:100]}...")
        
        # 判断是否是实质性内容
        if char_count > 50 and sentence_count > 0:
            if any(keyword in section for keyword in ['应', '必须', '检查', '注意', '禁止']):
                substantial_sections.append(section)
                print(f"    [实质性] 包含操作要求")
            elif any(keyword in section for keyword in ['系统', '设备', '机构', '装置']):
                substantial_sections.append(section)
                print(f"    [实质性] 包含设备描述")
            else:
                print(f"    [一般] 描述性内容")
        else:
            print(f"    [简短] 内容过短")
    
    print(f"\n[实质性段落] 共 {len(substantial_sections)} 个段落包含实质性内容")
    
    return substantial_sections

def provide_improvement_suggestions(content):
    """提供改进建议"""
    print("\n" + "=" * 60)
    print("改进建议")
    print("=" * 60)
    
    char_count = len(content)
    
    print("[可能的失败原因]:")
    reasons = []
    
    if '表' in content and content.count('表') > 5:
        reasons.append("1. 内容包含大量表格引用，但缺少表格具体内容")
    
    if content.count('注意事项') > 3:
        reasons.append("2. 内容主要是注意事项列表，缺少系统性知识")
    
    if content.count('检查') > 10:
        reasons.append("3. 内容主要是检查项目，难以形成完整的题目")
    
    if '概述' in content and '规范' in content:
        reasons.append("4. 内容混合了概述和技术规范，主题不够集中")
    
    if char_count > 6000:
        reasons.append("5. 内容过长且分散，LLM难以提取核心知识点")
    
    for reason in reasons:
        print(f"  {reason}")
    
    print(f"\n[改进建议]:")
    suggestions = [
        "1. 将长内容按主题分割成更小的片段",
        "2. 分离表格引用和实际内容",
        "3. 将注意事项和操作规程分开处理",
        "4. 针对不同类型的内容使用不同的题目生成策略",
        "5. 考虑增加更多上下文信息",
        "6. 调整LLM提示词，专注于特定类型的内容"
    ]
    
    for suggestion in suggestions:
        print(f"  {suggestion}")

def main():
    """主函数"""
    print("[分析] 失败片段生成失败原因分析")
    print("[文件] chunk5_20250619_151730_475.txt")
    
    # 读取失败片段
    content = read_failed_chunk()
    
    if content:
        # 分析内容特征
        analyze_content_characteristics(content)
        
        # 分析内容分段
        substantial_sections = analyze_content_sections(content)
        
        # 直接测试LLM
        test_with_llm_directly(content)
        
        # 提供改进建议
        provide_improvement_suggestions(content)
        
        print(f"\n" + "=" * 60)
        print("分析总结")
        print("=" * 60)
        
        print("[关键发现]:")
        print("1. 内容长度: 6418字符，内容丰富")
        print("2. 内容类型: 混合了注意事项、操作规程、设备概述、技术规范")
        print("3. 结构复杂: 包含多个不同主题的内容段落")
        print("4. 表格引用: 包含多个表格引用但缺少具体内容")
        
        print(f"\n[可能原因]:")
        print("- 内容过于分散，缺少集中的主题")
        print("- 混合了多种不同类型的内容")
        print("- LLM难以从如此复杂的内容中提取一致的知识点")
        print("- 表格引用较多但缺少实际数据")
        
        print(f"\n[建议解决方案]:")
        print("1. 按主题将内容分割成更小的片段")
        print("2. 分别处理注意事项、操作规程、设备概述等不同类型")
        print("3. 调整LLM提示词，专注于特定类型的内容生成")
        print("4. 考虑增加分块大小限制，避免内容过于复杂")
        
    else:
        print("[错误] 无法读取失败片段内容")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 分析程序异常: {str(e)}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的INI写入器测试
"""

import os
import sys
import tempfile
import configparser

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from output_manager.ini_writer import INIWriter
from config import Config

def test_simple_ini_writer():
    """简单测试INI写入器"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建配置
    config = Config()
    config.OUTPUT_DIR = temp_dir
    
    # 创建INI写入器
    writer = INIWriter(config)
    
    # 测试题目
    questions = [
        {
            'type': '单选题',
            'question': '测试问题1',
            'options': '选项A|选项B|选项C',
            'answer': 'A',
            'difficulty': '简单',
            'explanation': '这是解析',
            'knowledge_points': '知识点1|知识点2'
        }
    ]
    
    # 保存题目
    file_path = writer.save_questions_to_ini(questions)
    print(f"保存文件: {file_path}")
    
    # 检查文件是否存在
    if os.path.exists(file_path):
        print("文件创建成功")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print("文件内容:")
            print(content)
            
        # 尝试用configparser解析
        try:
            config_parser = configparser.ConfigParser()
            config_parser.read(file_path, encoding='utf-8')
            print(f"成功解析，找到 {len(config_parser.sections())} 个section")
            for section in config_parser.sections():
                print(f"Section: {section}")
        except Exception as e:
            print(f"解析失败: {e}")
    else:
        print("文件创建失败")
    
    # 清理
    if os.path.exists(file_path):
        os.remove(file_path)
    os.rmdir(temp_dir)

if __name__ == '__main__':
    test_simple_ini_writer()

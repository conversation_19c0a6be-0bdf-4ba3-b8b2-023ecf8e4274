"""
新模板.ini格式题库生成最终演示
展示完整的LLM API集成和INI格式输出功能
"""

import os
import sys
import requests
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from output_manager.ini_writer import INIWriter

def generate_quiz_with_api():
    """使用API生成题库"""
    print("=" * 60)
    print("新模板.ini格式题库生成演示")
    print("=" * 60)
    
    # API配置
    api_url = "http://************:3001/v1/chat/completions"
    api_key = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    model = "rsv-rcgjfz7v"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 示例文档内容
    document_content = """
企业数字化转型管理办法

第一章 总则
第一条 为规范企业数字化转型工作，提升企业数字化水平，制定本办法。
第二条 本办法适用于各类企业的数字化转型活动。

第二章 转型原则
第三条 企业数字化转型应遵循以下原则：
（一）统筹规划，分步实施
（二）技术驱动，业务导向
（三）数据为核心，安全为保障
（四）开放合作，协同发展

第三章 实施要求
第四条 企业应建立数字化转型领导小组，负责转型工作的统筹协调。
第五条 企业应制定数字化转型规划，明确转型目标、路径和时间表。
第六条 企业应加强数字化人才培养，提升员工数字化素养。

第四章 保障措施
第七条 企业应建立数字化转型评估机制，定期评估转型效果。
第八条 企业应加强数据安全管理，确保数据安全和隐私保护。
"""
    
    # 构建提示词
    prompt = f"""请根据以下文档内容生成题目，严格按照新模板.ini格式要求。要求生成以下数量的题目：
- 单选题：2道
- 多选题：1道
- 判断题：1道
- 填空题：1道
- 简答题：1道

**文档来源信息：**
文件名：企业数字化转型管理办法.txt

**新模板.ini格式填写规范：**
1. 题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】
2. 题目难度：简单/一般/较难/困难
3. 知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字
4. 单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L；正确答案请填写大写A B C D E F G H I J K L
5. 判断题：正确答案填写"对或错"
6. 填空题：题目中用【】表示一个空，最多支持12个空，每个空可设置3个备选答案，多个备选答案用"/"分隔
7. **简答题特殊要求**：关键词用作系统阅卷使用，最多支持12个关键词；关键词不得相同；**关键词应该是浓缩的核心概念，而不是单个字**

**简答题关键词生成要求**：
- 每个关键词应该是2-6个字的核心概念或术语
- 为每个关键词提供2-3个备选答案，用"/"分隔
- 关键词应涵盖答案的主要知识点
- 示例格式：关键字1：核心概念/主要内容/重点要素

**输出格式要求**：
请直接输出INI格式的题目，不要使用JSON格式。严格按照以下格式：

1.【单选题】题目内容
A、选项内容
B、选项内容
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：根据《企业数字化转型管理办法》第X条规定...
知识点：知识点1|知识点2

2.【简答题】题目内容
关键字1：核心概念1/主要内容1/重点要素1
关键字2：核心概念2/主要内容2/重点要素2
关键字3：核心概念3/主要内容3/重点要素3
题目难度：较难
作答上传图片：否
答案解析：根据《企业数字化转型管理办法》...
知识点：知识点1|知识点2|知识点3

文档内容：
{document_content}
"""
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的题目生成助手。请严格按照要求的格式输出，确保简答题的关键词是核心概念而不是单个字。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": 3000,
        "temperature": 0.7
    }
    
    try:
        print("正在调用LLM API生成题目...")
        response = requests.post(api_url, headers=headers, json=data, timeout=120)
        
        if response.status_code == 200:
            response_json = response.json()
            content = response_json['choices'][0]['message']['content']
            
            print("[成功] LLM API调用成功！")
            print(f"生成内容长度: {len(content)} 字符")
            
            return content
        else:
            print(f"[失败] API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None

    except Exception as e:
        print(f"[错误] API调用异常: {str(e)}")
        return None

def save_to_ini_format(content):
    """保存为INI格式"""
    print("\n" + "=" * 60)
    print("保存为新模板.ini格式")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供保存")
        return None
    
    # 创建配置
    config = Config()
    config.OUTPUT_DIR = "final_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建INI写入器
    ini_writer = INIWriter(config)
    
    # 简单解析生成的内容（这里我们直接保存原始内容）
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"企业数字化转型题库_{timestamp}.ini"
    file_path = os.path.join(config.OUTPUT_DIR, filename)
    
    try:
        # 添加填写规范头部
        header = """填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；关键词不得相同
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/"，请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案"a"、"b/b"、"c//c"。

"""
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(header + content)
        
        print(f"[成功] 题库已保存为INI格式: {file_path}")

        # 显示文件内容预览
        print(f"\n[预览] 生成的题库内容预览:")
        print("-" * 40)
        print(content[:1500])  # 显示前1500字符
        if len(content) > 1500:
            print("...")
            print(f"[完整内容请查看文件: {file_path}]")

        return file_path

    except Exception as e:
        print(f"[错误] 保存文件失败: {str(e)}")
        return None

def analyze_generated_content(content):
    """分析生成的内容"""
    print("\n" + "=" * 60)
    print("题库内容分析")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供分析")
        return
    
    # 统计题目数量
    import re
    
    single_choice = len(re.findall(r'【单选题】', content))
    multiple_choice = len(re.findall(r'【多选题】', content))
    true_false = len(re.findall(r'【判断题】', content))
    fill_blank = len(re.findall(r'【填空题】', content))
    short_answer = len(re.findall(r'【简答题】', content))
    
    total = single_choice + multiple_choice + true_false + fill_blank + short_answer
    
    print(f"[统计] 题目统计:")
    print(f"  单选题: {single_choice} 道")
    print(f"  多选题: {multiple_choice} 道")
    print(f"  判断题: {true_false} 道")
    print(f"  填空题: {fill_blank} 道")
    print(f"  简答题: {short_answer} 道")
    print(f"  总计: {total} 道")

    # 检查简答题关键词格式
    if short_answer > 0:
        print(f"\n[检查] 简答题关键词格式检查:")
        keyword_matches = re.findall(r'关键字\d+：([^/\n]+)', content)
        if keyword_matches:
            print(f"  发现 {len(keyword_matches)} 个关键词")
            for i, keyword in enumerate(keyword_matches[:5], 1):  # 显示前5个
                keyword_length = len(keyword.strip())
                status = "[OK]" if 2 <= keyword_length <= 6 else "[警告]"
                print(f"  关键词{i}: '{keyword.strip()}' ({keyword_length}字) {status}")
        else:
            print("  [警告] 未找到标准格式的关键词")

    print(f"\n[完成] 内容分析完成")

def main():
    """主演示函数"""
    print("[演示] 新模板.ini格式题库生成系统演示")
    print("[时间] " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("[API] http://************:3001")
    print("[模型] rsv-rcgjfz7v")
    
    # 步骤1: 生成题目
    content = generate_quiz_with_api()
    
    if content:
        # 步骤2: 分析内容
        analyze_generated_content(content)
        
        # 步骤3: 保存为INI格式
        ini_file = save_to_ini_format(content)
        
        if ini_file:
            print(f"\n[成功] 演示完成！")
            print(f"[文件] 生成的题库文件: {ini_file}")
            print(f"[特点]:")
            print(f"   [OK] 严格按照新模板.ini格式")
            print(f"   [OK] 简答题使用关键词格式（核心概念，非单个字）")
            print(f"   [OK] 包含完整的填写规范")
            print(f"   [OK] 支持系统导入和自动阅卷")
        else:
            print(f"\n[失败] 保存失败")
    else:
        print(f"\n[失败] 题目生成失败")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 演示程序异常: {str(e)}")
        import traceback
        traceback.print_exc()

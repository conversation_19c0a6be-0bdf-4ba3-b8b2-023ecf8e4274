"""
进度保存模块
定时保存处理进度，防止数据丢失
"""

import os
import json
import logging
import pickle
from typing import List, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ProgressSaver:
    """进度保存器"""
    
    def __init__(self, config):
        """
        初始化进度保存器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.output_dir = config.OUTPUT_DIR
        self.save_interval = config.PROGRESS_SAVE_INTERVAL
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 进度文件路径
        self.progress_file = os.path.join(self.output_dir, "processing_progress.json")
        self.backup_dir = os.path.join(self.output_dir, "backups")
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def save_progress(self, processed_chunks: List[Dict[str, Any]], 
                     generated_questions: List[Dict[str, Any]], 
                     failed_chunks: List[Dict[str, Any]] = None,
                     current_index: int = 0) -> str:
        """
        保存当前处理进度
        
        Args:
            processed_chunks: 已处理的文本块
            generated_questions: 已生成的题目
            failed_chunks: 失败的文本块
            current_index: 当前处理索引
            
        Returns:
            进度文件路径
        """
        if failed_chunks is None:
            failed_chunks = []
        
        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'current_index': current_index,
            'total_processed': len(processed_chunks),
            'total_questions': len(generated_questions),
            'total_failed': len(failed_chunks),
            'processed_chunks': processed_chunks,
            'generated_questions': generated_questions,
            'failed_chunks': failed_chunks
        }
        
        try:
            # 保存主进度文件
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            # 创建时间戳备份
            backup_filename = f"progress_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"进度已保存: 处理 {len(processed_chunks)} 个块，生成 {len(generated_questions)} 道题目")
            return self.progress_file
            
        except Exception as e:
            logger.error(f"保存进度失败: {str(e)}")
            raise
    
    def load_progress(self) -> Dict[str, Any]:
        """
        加载之前的处理进度
        
        Returns:
            进度数据，如果没有进度文件则返回空字典
        """
        if not os.path.exists(self.progress_file):
            logger.info("没有找到进度文件，从头开始处理")
            return {}
        
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            logger.info(f"加载进度: 已处理 {progress_data.get('total_processed', 0)} 个块，"
                       f"生成 {progress_data.get('total_questions', 0)} 道题目")
            return progress_data
            
        except Exception as e:
            logger.error(f"加载进度失败: {str(e)}")
            return {}
    
    def should_save_progress(self, current_count: int) -> bool:
        """
        判断是否应该保存进度
        
        Args:
            current_count: 当前处理数量
            
        Returns:
            是否应该保存
        """
        return current_count % self.save_interval == 0
    
    def clear_progress(self):
        """清除进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                logger.info("进度文件已清除")
        except Exception as e:
            logger.error(f"清除进度文件失败: {str(e)}")
    
    def create_checkpoint(self, checkpoint_name: str, data: Dict[str, Any]) -> str:
        """
        创建检查点
        
        Args:
            checkpoint_name: 检查点名称
            data: 要保存的数据
            
        Returns:
            检查点文件路径
        """
        checkpoint_filename = f"checkpoint_{checkpoint_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        checkpoint_path = os.path.join(self.backup_dir, checkpoint_filename)
        
        try:
            with open(checkpoint_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.info(f"检查点已创建: {checkpoint_path}")
            return checkpoint_path
            
        except Exception as e:
            logger.error(f"创建检查点失败: {str(e)}")
            raise
    
    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """
        加载检查点
        
        Args:
            checkpoint_path: 检查点文件路径
            
        Returns:
            检查点数据
        """
        try:
            with open(checkpoint_path, 'rb') as f:
                data = pickle.load(f)
            
            logger.info(f"检查点已加载: {checkpoint_path}")
            return data
            
        except Exception as e:
            logger.error(f"加载检查点失败: {str(e)}")
            raise
    
    def list_backups(self) -> List[str]:
        """
        列出所有备份文件
        
        Returns:
            备份文件路径列表
        """
        backups = []
        
        if os.path.exists(self.backup_dir):
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.json') or filename.endswith('.pkl'):
                    backups.append(os.path.join(self.backup_dir, filename))
        
        backups.sort(reverse=True)  # 按时间倒序
        return backups
    
    def cleanup_old_backups(self, keep_count: int = 10):
        """
        清理旧的备份文件
        
        Args:
            keep_count: 保留的备份文件数量
        """
        backups = self.list_backups()
        
        if len(backups) > keep_count:
            files_to_delete = backups[keep_count:]
            
            for file_path in files_to_delete:
                try:
                    os.remove(file_path)
                    logger.info(f"删除旧备份: {file_path}")
                except Exception as e:
                    logger.error(f"删除备份文件失败 {file_path}: {str(e)}")
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """
        获取进度摘要
        
        Returns:
            进度摘要信息
        """
        progress_data = self.load_progress()
        
        if not progress_data:
            return {
                'has_progress': False,
                'message': '没有找到进度文件'
            }
        
        return {
            'has_progress': True,
            'timestamp': progress_data.get('timestamp', ''),
            'current_index': progress_data.get('current_index', 0),
            'total_processed': progress_data.get('total_processed', 0),
            'total_questions': progress_data.get('total_questions', 0),
            'total_failed': progress_data.get('total_failed', 0),
            'can_resume': True
        }

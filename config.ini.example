# 文档到题库工具配置文件示例
# 复制此文件为 config.ini 并修改相应配置

[api]
# API基础URL - 可以是OpenAI官方或任何兼容的API端点
base_url = https://api.openai.com/v1
# API密钥
key = your_api_key_here
# 模型名称
model = gpt-3.5-turbo
# 生成回答的最大token数
max_tokens = 2000
# 生成温度，控制随机性 (0-1)
temperature = 0.7
# 请求超时时间（秒）
timeout = 60
# 最大重试次数
max_retries = 3

[processing]
# 每个分块的最大字符数
max_chunk_size = 4000
# 每个分块生成的题目数量
questions_per_chunk = 3

[output]
# 输出目录
dir = output
# 错误文档目录
error_dir = error_docs
# 每处理多少个分块保存一次进度
save_interval = 10
# INI批量保存数量
ini_batch_save_count = 30
# 自动备份间隔
auto_backup_interval = 200

# 常用API端点配置示例：
# 
# OpenAI官方:
# base_url = https://api.openai.com/v1
# model = gpt-3.5-turbo 或 gpt-4
#
# DeepSeek:
# base_url = https://api.deepseek.com/v1
# model = deepseek-chat
#
# 智谱AI:
# base_url = https://open.bigmodel.cn/api/paas/v4
# model = glm-4
#
# 月之暗面:
# base_url = https://api.moonshot.cn/v1
# model = moonshot-v1-8k
#
# 阿里云:
# base_url = https://dashscope.aliyuncs.com/compatible-mode/v1
# model = qwen-turbo

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试追加功能
"""

import os
import sys
import tempfile
import configparser
import logging

# 启用调试日志
logging.basicConfig(level=logging.DEBUG)

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from output_manager.ini_writer import INIWriter
from config import Config

def debug_append():
    """调试追加功能"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    # 创建配置
    config = Config()
    config.OUTPUT_DIR = temp_dir
    
    # 创建INI写入器
    writer = INIWriter(config)
    
    # 测试题目
    questions1 = [
        {
            'type': '单选题',
            'question': '测试问题1',
            'options': '选项A|选项B|选项C',
            'answer': 'A',
            'difficulty': '简单',
            'explanation': '这是解析1',
            'knowledge_points': '知识点1'
        }
    ]
    
    questions2 = [
        {
            'type': '多选题',
            'question': '测试问题2',
            'options': '选项A|选项B|选项C|选项D',
            'answer': 'A,B',
            'difficulty': '中等',
            'explanation': '这是解析2',
            'knowledge_points': '知识点2'
        }
    ]
    
    # 第一次保存
    print("第一次保存...")
    file_path1 = writer.append_questions(questions1)
    print(f"保存文件: {file_path1}")
    
    # 读取并显示内容
    with open(file_path1, 'r', encoding='utf-8') as f:
        content1 = f.read()
        print("第一次保存后的内容:")
        print(content1)
        print("-" * 50)
    
    # 测试获取最后题目编号
    last_num = writer._get_last_question_number_from_ini(file_path1)
    print(f"获取到的最后题目编号: {last_num}")

    # 解析第一次保存的内容
    config_parser = configparser.ConfigParser()
    config_parser.read(file_path1, encoding='utf-8')
    print("第一次保存的sections:")
    for section in config_parser.sections():
        print(f"  - {section}")
        # 提取编号
        if '.【' in section:
            number_str = section.split('.【')[0]
            print(f"    编号: {number_str}")

    # 第二次追加
    print("第二次追加...")
    file_path2 = writer.append_questions(questions2, filename=os.path.basename(file_path1))
    print(f"追加文件: {file_path2}")
    
    # 读取并显示最终内容
    with open(file_path2, 'r', encoding='utf-8') as f:
        content2 = f.read()
        print("追加后的内容:")
        print(content2)
        print("-" * 50)
    
    # 用configparser解析
    try:
        config_parser = configparser.ConfigParser()
        config_parser.read(file_path2, encoding='utf-8')
        print(f"解析成功，找到 {len(config_parser.sections())} 个section")
        for section in config_parser.sections():
            print(f"Section: {section}")
    except Exception as e:
        print(f"解析失败: {e}")
    
    # 清理
    if os.path.exists(file_path2):
        os.remove(file_path2)
    os.rmdir(temp_dir)

if __name__ == '__main__':
    debug_append()

# 题库生成工具环境配置文件
# 复制此文件为 .env 并修改相应配置

# API配置
API_BASE_URL=http://************:3001
API_KEY=sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3
MODEL_NAME=rsv-rcgjfz7v
MAX_TOKENS=4000
TEMPERATURE=0.3
REQUEST_TIMEOUT=60
MAX_RETRIES=3

# 文档处理配置
MAX_CHUNK_SIZE=3000
MIN_TOKENS_FOR_GENERATION=100
QUESTIONS_PER_CHUNK=6
DISABLE_DOCUMENT_SPLITTING=false
ENABLE_CHUNK_MERGING=true

# 题型数量配置
SINGLE_CHOICE_COUNT=2
MULTIPLE_CHOICE_COUNT=1
FILL_BLANK_COUNT=1
SHORT_ANSWER_COUNT=1
TRUE_FALSE_COUNT=1
SORTING_COUNT=0

# 输出配置
OUTPUT_DIR=output
ERROR_DIR=error_docs
CSV_FILENAME=quiz_results.csv

# 常用的API端点示例：
# OpenAI官方: https://api.openai.com/v1
# DeepSeek: https://api.deepseek.com/v1
# 智谱AI: https://open.bigmodel.cn/api/paas/v4
# 月之暗面: https://api.moonshot.cn/v1
# 阿里云: https://dashscope.aliyuncs.com/compatible-mode/v1

from typing import Any, Dict

class QuestionCalculator:
    def __init__(self, config):
        self.config = config

    def calculate_question_counts(self, char_count: int) -> Dict[str, int]:
        base_threshold = self.config.BASE_CHAR_THRESHOLD
        question_counts = {}

        # 定义题型到配置属性的映射
        question_type_map = {
            "单选题": self.config.BASE_SINGLE_CHOICE_COUNT,
            "多选题": self.config.BASE_MULTIPLE_CHOICE_COUNT,
            "填空题": self.config.BASE_FILL_BLANK_COUNT,
            "问答题": self.config.BASE_SHORT_ANSWER_COUNT,
            "判断题": self.config.BASE_TRUE_FALSE_COUNT,
            "排序题": self.config.BASE_SORTING_COUNT,
        }

        for question_type, base_count_value in question_type_map.items():
            if char_count <= base_threshold:
                question_counts[question_type] = base_count_value
            else:
                question_counts[question_type] = max(1, int(base_count_value * (char_count / base_threshold)))

        return question_counts

    def get_question_counts_for_chunk(self, chunk: Dict[str, Any]) -> Dict[str, int]:
        char_count = len(chunk['content'])
        return self.calculate_question_counts(char_count)
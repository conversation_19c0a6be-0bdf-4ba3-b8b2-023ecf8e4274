import os
import sys
import logging
from typing import List, Dict, Any, Optional, Callable

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from document_processor.doc_reader import DocumentReader
from document_processor.text_splitter import TextSplitter
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.ini_writer import INIWriter
from output_manager.incremental_saver import IncrementalSaver
from output_manager.progress_saver import ProgressSaver
from error_handler.error_manager import ErrorManager
from utils.helpers import (
    setup_logging, validate_input_directory, create_argument_parser,
    print_banner, print_progress_bar, validate_api_config,
    estimate_processing_time, summarize_results, scan_directory_structure
)
from utils.question_calculator import QuestionCalculator

logger = logging.getLogger(__name__)

class DocumentToQuizProcessor:
    """文档到题库处理器主类"""

    def __init__(self, config: Config, progress_callback: Optional[Callable] = None):
        """
        初始化处理器

        Args:
            config: 配置对象
            progress_callback: 用于向GUI发送进度和题目更新的回调函数
        """
        self.config = config
        self.progress_callback = progress_callback # 保存回调函数

        # 初始化各个组件
        # 根据配置获取启用的文件格式
        enabled_formats = config.get_enabled_formats()
        self.doc_reader = DocumentReader(enabled_formats=enabled_formats)
        self.text_splitter = TextSplitter(
            max_chunk_size=config.MAX_CHUNK_SIZE,
            disable_splitting=config.DISABLE_DOCUMENT_SPLITTING,
            enable_chunk_merging=config.ENABLE_CHUNK_MERGING,
            use_new_splitting_logic=getattr(config, 'USE_NEW_SPLITTING_LOGIC', False)
        )
        self.llm_client = OpenAICompatibleClient(config)
        self.ini_writer = INIWriter(config)  # 新增INI输出器
        self.incremental_saver = IncrementalSaver(config, self.ini_writer)
        self.progress_saver = ProgressSaver(config)
        self.error_manager = ErrorManager(config)

        # 初始化题目数量计算器
        self.question_calculator = QuestionCalculator(config)

        logger.info("文档到题库处理器初始化完成")

    def process_documents(self, input_dir: str, resume: bool = False, recursive: bool = True) -> Dict[str, Any]:
        """
        处理文档目录

        Args:
            input_dir: 输入目录
            resume: 是否从上次中断处继续
            recursive: 是否递归处理子文件夹，默认为True

        Returns:
            处理结果摘要
        """
        logger.info(f"开始处理文档目录: {input_dir} (递归: {recursive})")

        # 先扫描目录结构，显示统计信息
        enabled_formats = self.config.get_enabled_formats()
        structure_info = scan_directory_structure(input_dir, recursive, enabled_formats)
        if 'error' not in structure_info:
            logger.info(f"目录扫描结果: 总文件 {structure_info['total_files']} 个，支持的文档 {structure_info['supported_files']} 个")
            logger.info(f"文件类型分布: {structure_info['file_types']}")

        # 检查是否需要恢复进度
        if resume:
            progress_data = self.progress_saver.load_progress()
            if progress_data:
                return self._resume_processing(input_dir, progress_data, recursive)

        # 全新开始处理
        return self._start_fresh_processing(input_dir, recursive)

    def _start_fresh_processing(self, input_dir: str, recursive: bool = True) -> Dict[str, Any]:
        """开始全新的处理流程"""
        # 1. 读取文档
        logger.info("步骤 1/4: 读取文档...")
        documents = self.doc_reader.batch_read_documents(input_dir, recursive)

        if not documents:
            logger.error("没有找到可处理的文档")
            return {'error': '没有找到可处理的文档'}

        logger.info(f"成功读取 {len(documents)} 个文档")

        # 2. 分割文本
        logger.info("步骤 2/4: 分割文本...")
        chunks = self.text_splitter.batch_split_documents(documents)

        if not chunks:
            logger.error("文本分割失败")
            return {'error': '文本分割失败'}

        logger.info(f"文本分割完成，共生成 {len(chunks)} 个文本块")

        # 估算处理时间
        estimated_time = estimate_processing_time(len(chunks), self.config.QUESTIONS_PER_CHUNK)
        logger.info(f"预计处理时间: {estimated_time}")

        # 3. 生成题目
        logger.info("步骤 3/4: 生成题目...")
        all_questions, failed_chunks, successful_chunk_indices = self._process_chunks(chunks)

        # 4. 保存结果
        logger.info("步骤 4/4: 保存结果...")
        result = self._save_results(all_questions, failed_chunks, successful_chunk_indices)

        # 清理进度文件
        self.progress_saver.clear_progress()

        return result

    def _resume_processing(self, input_dir: str, progress_data: Dict[str, Any], recursive: bool = True) -> Dict[str, Any]:
        """从上次中断处恢复处理"""
        logger.info("从上次中断处恢复处理...")

        # 获取已处理的数据
        processed_chunks = progress_data.get('processed_chunks', [])
        generated_questions = progress_data.get('generated_questions', [])
        failed_chunks = progress_data.get('failed_chunks', [])
        current_index = progress_data.get('current_index', 0)
        successful_chunk_indices = progress_data.get('successful_chunk_indices', [])

        logger.info(f"已处理 {len(processed_chunks)} 个文本块，生成 {len(generated_questions)} 道题目")

        # 重新读取文档并分割
        documents = self.doc_reader.batch_read_documents(input_dir, recursive)
        all_chunks = self.text_splitter.batch_split_documents(documents)

        # 处理剩余的文本块
        remaining_chunks = all_chunks[current_index:]

        if remaining_chunks:
            logger.info(f"继续处理剩余的 {len(remaining_chunks)} 个文本块")
            new_questions, new_failed, new_successful_chunk_indices = self._process_chunks(remaining_chunks, current_index)

            # 合并结果
            generated_questions.extend(new_questions)
            failed_chunks.extend(new_failed)
            successful_chunk_indices.extend(new_successful_chunk_indices)

        # 保存最终结果
        result = self._save_results(generated_questions, failed_chunks, successful_chunk_indices)

        # 清理进度文件
        self.progress_saver.clear_progress()

        return result

    def _process_chunks(self, chunks: List[Dict[str, Any]], start_index: int = 0) -> tuple:
        """处理文本块生成题目，支持增量保存和智能合并"""
        all_questions = []
        failed_chunks = []
        successful_chunk_indices = []

        total_chunks = len(chunks)
        i = 0  # 使用while循环以支持动态跳过已合并的分块

        while i < len(chunks):
            current_index = start_index + i
            chunk = chunks[i]

            try:
                # 显示进度
                if self.progress_callback:
                    self.progress_callback({
                        'type': 'progress',
                        'data': {
                            'current': i + 1,
                            'total': total_chunks,
                            'filename': chunk.get('filename', '未知文件')
                        }
                    })

                # 根据字符数计算题目数量
                question_counts = self.question_calculator.get_question_counts_for_chunk(chunk)

                # --- 第一步：尝试直接生成题目 ---
                logger.info(f"尝试直接生成题目 (原始分块): {chunk['filename']} (块 {chunk['chunk_index']})")
                quiz_data = self.llm_client.generate_quiz(
                    content=chunk['content'],
                    source_filename=chunk.get('filename', '未知文档'),
                    question_counts=question_counts
                )

                if quiz_data and quiz_data.get('questions'):
                    # 成功生成，处理并进入下一个分块
                    self._handle_successful_generation(quiz_data, chunk, all_questions, successful_chunk_indices)
                    if self.progress_callback: # 发送生成的题目到GUI
                        self.progress_callback({
                            'type': 'question',
                            'data': quiz_data # 整个quiz_data，包含questions键
                        })
                    i += 1
                    continue

                # --- 第二步：如果直接生成失败，根据配置决定下一步 ---
                logger.warning(f"初步生成题目失败: {chunk['filename']} (块 {chunk['chunk_index']}). 尝试下一步策略。")

                if self.config.ENABLE_CHUNK_MERGING: # 检查是否启用了分块合并
                    # 尝试合并分块
                    logger.debug(f"尝试合并分块以生成足够内容: {chunk['filename']} 索引 {chunk['chunk_index']}")
                    merged_result = self._try_merge_chunks_for_generation(chunks, i)

                    if merged_result['success']:
                        merged_chunk = merged_result['merged_chunk']
                        next_index_after_merge = merged_result['next_index']
                        original_char_count = merged_result['original_char_count']
                        merged_char_count = merged_result['merged_char_count']

                        logger.info(f"分块合并成功: 从索引 {i} 合并到 {next_index_after_merge - 1}, "
                                   f"字符数从 {original_char_count} 增加到 {merged_char_count}")
                        if self.progress_callback: # 发送合并信息到GUI
                            self.progress_callback({
                                'type': 'merge_info',
                                'data': {
                                    'filename': merged_chunk.get('filename', '未知文件'),
                                    'original_index': i,
                                    'merged_to_index': next_index_after_merge - 1,
                                    'char_count': merged_char_count
                                }
                            })

                        # 根据合并后的字符数计算题目数量
                        merged_question_counts = self.question_calculator.get_question_counts_for_chunk(merged_chunk)

                        # 尝试使用合并后的分块生成题目
                        logger.info(f"尝试使用合并后的分块生成题目: {merged_chunk['filename']} (块 {merged_chunk['chunk_index']})")
                        merged_quiz_data = self.llm_client.generate_quiz(
                            content=merged_chunk['content'],
                            source_filename=merged_chunk.get('filename', '未知文档'),
                            question_counts=merged_question_counts
                        )

                        if merged_quiz_data and merged_quiz_data.get('questions'):
                            # 合并后成功生成，处理并跳过已合并的分块
                            logger.info(f"合并后成功生成题目: {merged_chunk['filename']}")
                            self._handle_successful_generation(merged_quiz_data, merged_chunk, all_questions, successful_chunk_indices)
                            if self.progress_callback: # 发送生成的题目到GUI
                                self.progress_callback({
                                    'type': 'question',
                                    'data': merged_quiz_data
                                })
                            i = next_index_after_merge
                            continue

                        # --- 第三步：合并后生成失败，无论字数是否超过限制，都尝试降级 ---
                        logger.warning(f"合并后生成题目失败: {merged_chunk['filename']} (块 {merged_chunk['chunk_index']}). 尝试降级策略。")
                        downgraded_quiz_data = self._attempt_generate_quiz_with_downgrade(
                            content=merged_chunk['content'],
                            source_filename=merged_chunk.get('filename', '未知文档'),
                            initial_question_counts=merged_question_counts
                        )

                        if downgraded_quiz_data and downgraded_quiz_data.get('questions'):
                            # 降级成功，处理并跳过已合并的分块
                            logger.info(f"降级策略成功生成题目: {merged_chunk['filename']}")
                            self._handle_successful_generation(downgraded_quiz_data, merged_chunk, all_questions, successful_chunk_indices)
                            if self.progress_callback: # 发送生成的题目到GUI
                                self.progress_callback({
                                    'type': 'question',
                                    'data': downgraded_quiz_data
                                })
                            i = next_index_after_merge
                            continue
                        else:
                            # 降级失败，立即走失败流程
                            logger.warning(f"所有降级尝试均未能生成题目，立即标记为失败: {merged_chunk['filename']}")
                            self._handle_failed_chunk(merged_chunk, 'all_downgrade_attempts_failed_after_merge', all_questions, failed_chunks)
                            if self.progress_callback: # 发送失败信息到GUI
                                self.progress_callback({
                                    'type': 'failure',
                                    'data': {
                                        'filename': merged_chunk.get('filename', '未知文件'),
                                        'chunk_index': merged_chunk.get('chunk_index', 'N/A'),
                                        'reason': 'all_downgrade_attempts_failed_after_merge',
                                        'chunk_content': merged_chunk.get('content', '无内容')
                                    }
                                })
                            i = next_index_after_merge
                            continue
                    else:
                        # 合并失败，不记录错误，直接进入降级流程
                        logger.warning(f"分块合并失败: {merged_result.get('reason', '未知原因')}，尝试降级生成")
                        downgraded_quiz_data = self._attempt_generate_quiz_with_downgrade(
                            content=chunk['content'],
                            source_filename=chunk.get('filename', '未知文档'),
                            initial_question_counts=question_counts
                        )

                        if downgraded_quiz_data and downgraded_quiz_data.get('questions'):
                            # 降级成功
                            logger.info(f"降级策略成功生成题目: {chunk['filename']}")
                            self._handle_successful_generation(downgraded_quiz_data, chunk, all_questions, successful_chunk_indices)
                            if self.progress_callback: # 发送生成的题目到GUI
                                self.progress_callback({
                                    'type': 'question',
                                    'data': downgraded_quiz_data
                                })
                            i += 1
                            continue
                        else:
                            # 降级失败，标记为失败
                            logger.warning(f"降级生成失败，标记为失败: {chunk['filename']}")
                            self._handle_failed_chunk(chunk, 'downgrade_failed_after_merge_failure', all_questions, failed_chunks)
                            if self.progress_callback: # 发送失败信息到GUI
                                self.progress_callback({
                                    'type': 'failure',
                                    'data': {
                                        'filename': chunk.get('filename', '未知文件'),
                                        'chunk_index': chunk.get('chunk_index', 'N/A'),
                                        'reason': 'downgrade_failed_after_merge_failure',
                                        'chunk_content': chunk.get('content', '无内容')
                                    }
                                })
                            i += 1
                            continue
                else:
                    # 未启用分块合并，直接进入降级流程
                    logger.info(f"分块合并已禁用，直接尝试降级生成: {chunk['filename']}")
                    downgraded_quiz_data = self._attempt_generate_quiz_with_downgrade(
                        content=chunk['content'],
                        source_filename=chunk.get('filename', '未知文档'),
                        initial_question_counts=question_counts
                    )

                    if downgraded_quiz_data and downgraded_quiz_data.get('questions'):
                        # 降级成功
                        logger.info(f"降级策略成功生成题目: {chunk['filename']}")
                        self._handle_successful_generation(downgraded_quiz_data, chunk, all_questions, successful_chunk_indices)
                        if self.progress_callback: # 发送生成的题目到GUI
                            self.progress_callback({
                                'type': 'question',
                                'data': downgraded_quiz_data
                            })
                        i += 1
                        continue
                    else:
                        # 降级失败，标记为失败
                        logger.warning(f"降级生成失败，标记为失败: {chunk['filename']}")
                        self._handle_failed_chunk(chunk, 'downgrade_failed_no_merging', all_questions, failed_chunks)
                        if self.progress_callback: # 发送失败信息到GUI
                            self.progress_callback({
                                'type': 'failure',
                                'data': {
                                    'filename': chunk.get('filename', '未知文件'),
                                    'chunk_index': chunk.get('chunk_index', 'N/A'),
                                    'reason': 'downgrade_failed_no_merging',
                                    'chunk_content': chunk.get('content', '无内容')
                                }
                            })
                        i += 1
                        continue

            except Exception as e:
                logger.error(f"处理文本块时发生未知错误: {chunk.get('filename', '未知文件')} (块 {chunk.get('chunk_index', 'N/A')}) - 原因: {str(e)}")
                self._handle_failed_chunk(chunk, str(e), all_questions, failed_chunks)
                if self.progress_callback: # 发送异常失败信息到GUI
                    self.progress_callback({
                        'type': 'failure',
                        'data': {
                            'filename': chunk.get('filename', '未知文件'),
                            'chunk_index': chunk.get('chunk_index', 'N/A'),
                            'reason': str(e),
                            'chunk_content': chunk.get('content', '无内容')
                        }
                    })
                i += 1
                continue

        # 强制保存剩余的题目
        final_save_result = self.incremental_saver.force_save_all()
        if final_save_result.get('saved'):
            logger.info(f"最终保存: {final_save_result.get('questions_saved', 0)} 道题目")
            if self.progress_callback: # 发送最终保存信息到GUI
                self.progress_callback({
                    'type': 'final_save',
                    'data': {
                        'questions_saved': final_save_result.get('questions_saved', 0),
                        'total_saved': final_save_result.get('total_saved_count', 0),
                        'file_path': self.incremental_saver.get_status().get('main_file_path', '')
                    }
                })

        print()  # 进度条换行
        logger.info(f"文本块处理完成，成功生成 {len(all_questions)} 道题目，失败 {len(failed_chunks)} 个文本块")
        if self.progress_callback: # 发送完成状态到GUI
            self.progress_callback({
                'type': 'status',
                'data': f"文本块处理完成，成功生成 {len(all_questions)} 道题目，失败 {len(failed_chunks)} 个文本块"
            })

        return all_questions, failed_chunks, successful_chunk_indices

    def _attempt_generate_quiz_with_downgrade(self, content: str, source_filename: str, initial_question_counts: Dict[str, int]) -> Dict[str, Any]:
        """
        尝试生成题目，如果失败则进行最多3次降级重试。
        第一次降级是每种题型-1，最后一次保证每种题型至少一题。
        """
        quiz_data = None
        current_question_counts = initial_question_counts.copy()
        max_retries = 3 # 包括第一次尝试和两次降级

        for attempt in range(max_retries):
            # 记录当前尝试的题目数量
            logger.info(f"尝试生成题目 (第 {attempt + 1}/{max_retries} 次尝试), 题型数量: {current_question_counts}")

            quiz_data = self.llm_client.generate_quiz(
                content=content,
                source_filename=source_filename,
                question_counts=current_question_counts
            )

            if quiz_data and quiz_data.get('questions'):
                # 成功生成题目，返回结果
                logger.info(f"第 {attempt + 1} 次尝试成功生成题目。")
                return quiz_data
            else:
                logger.warning(f"第 {attempt + 1} 次尝试未能生成题目或返回空列表，尝试降级。")

                if attempt < max_retries - 1: # 不是最后一次尝试，可以降级
                    # 进行降级操作
                    new_question_counts = {}
                    has_downgraded = False
                    for q_type, count in current_question_counts.items():
                        if attempt == 0: # 第一次降级：每个题型-1
                            new_count = max(1, count - 1)
                        else: # 后续降级：进一步减少，但至少保证1题
                            # 可以根据需要调整这里的降级策略，例如除以2或更小的减量
                            new_count = max(1, count // 2 if count > 1 else count) # 例如，减半
                        if new_count < count:
                            has_downgraded = True
                        new_question_counts[q_type] = new_count
                    current_question_counts = new_question_counts
                    if not has_downgraded:
                        logger.warning(f"未能进一步降级题目数量，停止重试。")
                        break # 无法再降级，退出循环
                else:
                    logger.warning("已达到最大重试次数，且未能生成题目。")
                    break # 已是最后一次尝试，退出循环

        logger.warning(f"所有 {max_retries} 次尝试均未能成功生成题目。")
        return {'questions': []} # Always return a dict with 'questions' key

    def _try_merge_chunks_for_generation(self, chunks: List[Dict[str, Any]], current_index: int) -> Dict[str, Any]:
        """
        尝试合并分块以生成足够的内容

        Args:
            chunks: 所有分块列表
            current_index: 当前分块索引

        Returns:
            合并结果字典，包含success, merged_chunk, next_index
        """
        if current_index >= len(chunks):
            return {'success': False, 'reason': 'index_out_of_range'}

        current_chunk = chunks[current_index]
        current_char_count = self.text_splitter.count_characters(current_chunk['content'])

        # 如果当前分块已经足够大，不进行合并
        if current_char_count >= self.config.MAX_CHUNK_SIZE:
            logger.warning(f"分块字符数 {current_char_count} 已达到最大分块大小 {self.config.MAX_CHUNK_SIZE}，无法合并")
            return {'success': False, 'reason': 'exceeds_max_chunk_size'}

        # 使用TextSplitter的合并方法
        merged_chunk, next_index = self.text_splitter.try_merge_with_next_chunks(chunks, current_index)

        if merged_chunk is None:
            return {'success': False, 'reason': 'no_chunks_to_merge'}

        # 检查合并后的字符数
        merged_char_count = merged_chunk['char_count']

        logger.info(f"分块合并成功: 从索引 {current_index} 合并到 {next_index-1}, "
                   f"字符数从 {current_char_count} 增加到 {merged_char_count}")

        return {
            'success': True,
            'merged_chunk': merged_chunk,
            'next_index': next_index,
            'original_char_count': current_char_count,
            'merged_char_count': merged_char_count
        }

    def _handle_successful_generation(self, quiz_data: Dict[str, Any], chunk: Dict[str, Any], all_questions: List[Dict[str, Any]], successful_chunk_indices: List[int]):
        """处理成功生成题目的逻辑。"""
        chunk_questions = []
        for question in quiz_data['questions']:
            question['source_file'] = chunk['filename']
            question['chunk_index'] = chunk['chunk_index']
            if 'merged_from' in chunk:
                question['merged_from'] = chunk['merged_from']
            chunk_questions.append(question)
            all_questions.append(question)

        if quiz_data['questions']:
            if 'merged_from' in chunk and chunk['merged_from']:
                for original_idx in chunk['merged_from']:
                    successful_chunk_indices.append(original_idx)
            else:
                successful_chunk_indices.append(chunk['chunk_index'])

        save_result = self.incremental_saver.add_questions(chunk_questions)
        if save_result.get('saved'):
            logger.info(f"增量保存: {save_result.get('questions_saved', 0)} 道题目")
            if save_result.get('backup_info', {}).get('created'):
                logger.info(f"自动备份: {save_result['backup_info']['backup_path']}")
        logger.debug(f"成功生成 {len(quiz_data['questions'])} 道题目")

    def _handle_failed_chunk(self, chunk: Dict[str, Any], reason: str, all_questions: List[Dict[str, Any]], failed_chunks: List[Dict[str, Any]]):
        """处理文本块生成失败的逻辑。"""
        failed_chunk_data = {
            **chunk,
            'failure_reason': reason
        }
        self.error_manager.save_failed_chunk_realtime(chunk, reason)
        has_generated_questions = len(all_questions) > 0
        self.error_manager.save_failed_chunk(failed_chunk_data, reason, has_generated_questions=has_generated_questions)
        failed_chunks.append(failed_chunk_data)
        logger.warning(f"处理文本块失败: {chunk.get('filename', '未知文件')} (块 {chunk.get('chunk_index', 'N/A')}) - 原因: {reason}")

    def _save_results(self, questions: List[Dict[str, Any]], failed_chunks: List[Dict[str, Any]], successful_chunk_indices: List[int]) -> Dict[str, Any]:
        """保存处理结果（增量保存已处理主要保存工作）"""
        result_summary = summarize_results(questions, failed_chunks)

        try:
            # 获取增量保存的文件路径和状态
            incremental_status = self.incremental_saver.get_status()
            
            if incremental_status['main_file_path']:
                result_summary['ini_file'] = incremental_status['main_file_path']
                logger.info(f"INI题库文件: {incremental_status['main_file_path']}")
                logger.info(f"总共保存: {incremental_status['total_saved_count']} 道题目")
            else:
                # 如果增量保存没有文件，直接进行最终INI保存
                if questions:
                    final_ini_file = self.ini_writer.save_questions_to_ini(questions)
                    logger.info(f"最终保存 - INI题库已保存到: {final_ini_file}")
                    result_summary['ini_file'] = final_ini_file
                else:
                    result_summary['ini_file'] = "N/A"

            # 保存失败的文本块
            if failed_chunks and len(questions) == 0:
                error_summary_file = self.error_manager.create_error_summary(failed_chunks)
                logger.info(f"错误摘要已保存到: {error_summary_file}")
                result_summary['error_summary_file'] = error_summary_file
            else:
                logger.info("没有失败的文本块，无需保存错误摘要。")
                result_summary['error_summary_file'] = "N/A"

            # 由于增量保存处理了大部分保存，此处不再需要独立的摘要报告生成和保存逻辑
            # summary_report = self.csv_writer.generate_summary_report(questions)
            # summary_file = self.csv_writer.save_summary_report(summary_report)
            # logger.info(f"处理摘要已保存到: {summary_file}")
            # result_summary['summary_file'] = summary_file
            result_summary['summary_file'] = "摘要报告不再单独生成，主要结果在INI文件"

            # 添加增量保存统计信息
            result_summary['incremental_save_info'] = {
                'total_saved': incremental_status['total_saved_count'],
                'backup_count': incremental_status['backup_counter'],
                'batch_threshold': incremental_status['batch_save_threshold'],
                'auto_backup_interval': incremental_status['auto_backup_interval']
            }

            # 添加成功生成题目的文本块索引
            result_summary['successful_chunk_indices'] = successful_chunk_indices

            return result_summary

        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            result_summary['save_error'] = str(e)
            return result_summary

    def test_api_connection(self) -> bool:
        """测试API连接"""
        logger.info("测试API连接...")
        return self.llm_client.test_connection() 
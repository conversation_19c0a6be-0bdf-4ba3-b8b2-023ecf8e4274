#!/usr/bin/env python3
"""
基本集成测试 - 验证核心功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from output_manager.ini_writer import INIWriter
from output_manager.incremental_saver import IncrementalSaver
from utils.question_calculator import QuestionCalculator

def test_ini_writer_basic():
    """测试INI写入器基本功能"""
    print("测试INI写入器...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        config = Config()
        config.OUTPUT_DIR = temp_dir
        
        writer = INIWriter(config)
        
        # 测试数据
        test_questions = [
            {
                'type': '单选',
                'question': '测试单选题',
                'options': 'A选项|B选项|C选项|D选项',
                'answer': 'A',
                'difficulty': '简单',
                'explanation': '这是测试解析',
                'knowledge_points': ['测试知识点1', '测试知识点2']
            },
            {
                'type': '判断',
                'question': '这是一道测试判断题',
                'answer': '对',
                'difficulty': '简单',
                'explanation': '判断题解析',
                'knowledge_points': ['判断题知识点']
            }
        ]
        
        # 保存题目
        file_path = writer.save_questions_to_ini(test_questions, "test_basic.ini")
        
        # 验证文件存在
        assert os.path.exists(file_path), "INI文件未创建"
        
        # 验证文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            assert '1.【单选题】测试单选题' in content, "单选题格式错误"
            assert '2.【判断题】这是一道测试判断题' in content, "判断题格式错误"
            assert '正确答案=A' in content, "单选题答案错误"
            assert '正确答案=对' in content, "判断题答案错误"
        
        print("[OK] INI写入器测试通过")

def test_incremental_saver_basic():
    """测试增量保存器基本功能"""
    print("测试增量保存器...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config = Config()
        config.OUTPUT_DIR = temp_dir
        config.INI_BATCH_SAVE_COUNT = 2  # 设置较小的批次用于测试
        
        writer = INIWriter(config)
        saver = IncrementalSaver(config, writer)
        
        # 测试数据
        questions_batch1 = [
            {
                'type': '单选',
                'question': '批次1题目1',
                'options': 'A|B|C|D',
                'answer': 'A',
                'difficulty': '简单',
                'explanation': '解析1',
                'knowledge_points': ['知识点1']
            }
        ]
        
        questions_batch2 = [
            {
                'type': '判断',
                'question': '批次2题目1',
                'answer': '对',
                'difficulty': '简单',
                'explanation': '解析2',
                'knowledge_points': ['知识点2']
            }
        ]
        
        # 添加第一批题目（不应触发保存）
        result1 = saver.add_questions(questions_batch1)
        assert not result1['saved'], "第一批题目不应触发保存"
        
        # 添加第二批题目（应触发保存）
        result2 = saver.add_questions(questions_batch2)
        assert result2['saved'], "第二批题目应触发保存"
        
        # 验证状态
        status = saver.get_status()
        assert status['total_saved_count'] == 2, f"保存数量错误: {status['total_saved_count']}"
        assert status['main_file_path'] is not None, "主文件路径为空"
        
        print("[OK] 增量保存器测试通过")

def test_question_calculator_basic():
    """测试题目数量计算器基本功能"""
    print("测试题目数量计算器...")
    
    config = Config()
    calculator = QuestionCalculator(config)
    
    # 测试短文本
    short_counts = calculator.calculate_question_counts(500)  # 小于基础阈值
    assert sum(short_counts.values()) > 0, "短文本应生成题目"
    
    # 测试长文本
    long_counts = calculator.calculate_question_counts(2000)  # 大于基础阈值
    assert sum(long_counts.values()) > sum(short_counts.values()), "长文本应生成更多题目"
    
    print("✅ 题目数量计算器测试通过")

def main():
    """运行所有基本测试"""
    print("=" * 50)
    print("开始基本集成测试")
    print("=" * 50)
    
    try:
        test_ini_writer_basic()
        test_incremental_saver_basic()
        test_question_calculator_basic()
        
        print("\n" + "=" * 50)
        print("🎉 所有基本测试通过！")
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

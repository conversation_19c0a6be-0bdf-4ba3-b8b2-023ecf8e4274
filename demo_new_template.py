"""
新模板.ini格式演示脚本
展示按照新模板.ini格式生成题库的功能
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from output_manager.ini_writer import INIWriter

def create_demo_questions():
    """创建演示题目数据"""
    return [
        {
            'type': '单选',
            'question': '根据新模板.ini格式要求，单选题最多支持多少个选项？',
            'options': '4个选项（A、B、C、D）|6个选项（A到F）|8个选项（A到H）|12个选项（A到L）',
            'answer': 'D',
            'difficulty': '简单',
            'explanation': '根据《新模板填写规范》第4条规定，单选、多选题选项个数最多支持12个：A B C D E F G H I J K L。',
            'knowledge_points': ['新模板填写规范', '选项数量限制']
        },
        {
            'type': '多选',
            'question': '以下哪些是新模板.ini格式支持的题型？',
            'options': '单选题|多选题|判断题|填空题|简答题|排序题',
            'answer': 'ABCDE',
            'difficulty': '一般',
            'explanation': '依据《新模板使用指南》，支持单选题、多选题、判断题、填空题、简答题这五种题型。',
            'knowledge_points': ['新模板使用指南', '支持题型']
        },
        {
            'type': '判断',
            'question': '新模板.ini格式中，判断题的正确答案应该填写"对"或"错"。',
            'answer': '对',
            'difficulty': '简单',
            'explanation': '按照《新模板标准规范》第5条要求，判断题正确答案填写"对或错"。',
            'knowledge_points': ['新模板标准规范', '判断题答案格式']
        },
        {
            'type': '填空',
            'question': '新模板中，填空题用【】表示一个空，最多支持【12/十二/12个】个空，每个空可设置【3/三/3个】个备选答案，多个备选答案用【//斜杠/分隔符】分隔。',
            'answer': '12|3|/',
            'difficulty': '一般',
            'explanation': '根据《新模板技术规范》，这些是填空题的标准格式要求。',
            'knowledge_points': ['新模板技术规范', '填空题格式']
        },
        {
            'type': '简答',
            'question': '请简述新模板.ini格式相比传统格式的主要优势。',
            'answer': '标准化程度高；支持题型丰富；操作简单易用；系统兼容性好',
            'difficulty': '较难',
            'explanation': '根据《新模板设计理念》和《用户使用反馈报告》，新模板在这些方面都有显著提升。',
            'knowledge_points': ['新模板设计理念', '用户使用反馈', '格式优势']
        }
    ]

def main():
    """主演示函数"""
    print("=" * 60)
    print("新模板.ini格式题库生成演示")
    print("=" * 60)
    
    # 创建配置
    config = Config()
    config.OUTPUT_DIR = "demo_output"
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建INI写入器
    ini_writer = INIWriter(config)
    
    # 创建演示题目
    demo_questions = create_demo_questions()
    
    # 生成文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"demo_new_template_{timestamp}.ini"
    
    # 保存到INI文件
    ini_file = ini_writer.save_questions_to_ini(demo_questions, filename)
    
    print(f"[成功] 新模板.ini格式题库已生成: {ini_file}")
    print(f"[信息] 共生成 {len(demo_questions)} 道题目")
    
    # 生成摘要报告
    summary = ini_writer.generate_summary_report(demo_questions)
    print(f"\n[统计] 题目分布:")
    for key, value in summary.items():
        if key != 'total_questions':
            print(f"  {key}: {value} 道")
    
    # 显示文件内容预览
    print(f"\n[预览] 生成的INI文件内容（前1000字符）:")
    print("-" * 60)
    try:
        with open(ini_file, 'r', encoding='utf-8') as f:
            content = f.read()
        print(content[:1000])
        if len(content) > 1000:
            print("...")
            print(f"[信息] 完整内容请查看文件: {ini_file}")
    except Exception as e:
        print(f"[错误] 读取文件失败: {str(e)}")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)
    
    # 显示新模板.ini格式的主要特点
    print("\n[特点] 新模板.ini格式的主要优势:")
    print("1. 严格按照填写规范，确保格式标准化")
    print("2. 简答题使用关键词格式，便于系统阅卷")
    print("3. 支持多种题型，满足不同考试需求")
    print("4. 知识点标记清晰，便于分类管理")
    print("5. 兼容现有题库系统，易于导入使用")
    
    return ini_file

if __name__ == "__main__":
    try:
        result_file = main()
        print(f"\n[完成] 演示文件已保存: {result_file}")
    except Exception as e:
        print(f"[错误] 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()

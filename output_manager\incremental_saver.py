"""
增量保存管理器
负责累计题目数量达到阈值时自动写入文件，防止数据丢失
"""

import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from .ini_writer import INIWriter

logger = logging.getLogger(__name__)

class IncrementalSaver:
    """增量保存管理器"""

    def __init__(self, config, ini_writer: INIWriter):
        """
        初始化增量保存管理器

        Args:
            config: 配置对象
            ini_writer: INI写入器实例
        """
        self.config = config
        self.ini_writer = ini_writer
        
        # 配置参数
        self.batch_save_count = getattr(config, 'INI_BATCH_SAVE_COUNT', 5)
        self.auto_backup_interval = getattr(config, 'AUTO_BACKUP_INTERVAL', 50)  # 每50道题目创建备份
        
        # 状态管理
        self.accumulated_questions = []  # 累计的题目
        self.total_saved_count = 0  # 总共已保存的题目数
        self.current_batch_count = 0  # 当前批次累计数
        self.last_save_time = datetime.now()
        self.main_file_path = None  # 主文件路径
        self.backup_counter = 0  # 备份计数器
        
        logger.info(f"增量保存管理器初始化完成")
        logger.info(f"批量保存阈值: {self.batch_save_count} 道题目")
        logger.info(f"自动备份间隔: {self.auto_backup_interval} 道题目")

    def add_questions(self, questions: List[Dict[str, Any]], force_save: bool = False) -> Dict[str, Any]:
        """
        添加题目到累计池，达到阈值时自动保存

        Args:
            questions: 新生成的题目列表
            force_save: 是否强制保存

        Returns:
            保存结果信息
        """
        if not questions:
            return {'saved': False, 'reason': 'no_questions'}

        # 添加到累计池
        self.accumulated_questions.extend(questions)
        self.current_batch_count += len(questions)
        
        logger.debug(f"添加 {len(questions)} 道题目，当前累计: {self.current_batch_count}")

        result = {'saved': False, 'questions_added': len(questions)}

        # 检查是否需要保存
        should_save = (
            force_save or 
            self.current_batch_count >= self.batch_save_count or
            len(self.accumulated_questions) >= self.auto_backup_interval
        )

        if should_save:
            save_result = self._save_accumulated_questions()
            result.update(save_result)

        return result

    def _save_accumulated_questions(self) -> Dict[str, Any]:
        """
        保存累计的题目

        Returns:
            保存结果
        """
        if not self.accumulated_questions:
            return {'saved': False, 'reason': 'no_accumulated_questions'}

        try:
            questions_to_save = self.accumulated_questions.copy()
            save_count = len(questions_to_save)

            # 确定保存方式
            if self.main_file_path is None:
                # 第一次保存，创建新文件
                self.main_file_path = self.ini_writer.save_questions_to_ini(questions_to_save)
                self.ini_writer.set_current_file(self.main_file_path)
                save_type = 'create'
            else:
                # 追加到现有文件
                self.ini_writer.set_current_file(self.main_file_path)
                self.ini_writer.append_questions_to_ini(questions_to_save)
                save_type = 'append'

            # 更新状态
            self.total_saved_count += save_count
            self.current_batch_count = 0
            self.accumulated_questions.clear()
            self.last_save_time = datetime.now()

            # 检查是否需要创建备份
            backup_info = self._check_and_create_backup()

            logger.info(f"增量保存完成: {save_type} {save_count} 道题目，总计: {self.total_saved_count}")

            return {
                'saved': True,
                'save_type': save_type,
                'questions_saved': save_count,
                'total_saved': self.total_saved_count,
                'file_path': self.main_file_path,
                'backup_info': backup_info,
                'save_time': self.last_save_time.strftime("%Y-%m-%d %H:%M:%S")
            }

        except Exception as e:
            logger.error(f"增量保存失败: {str(e)}")
            return {
                'saved': False,
                'error': str(e),
                'questions_lost': len(self.accumulated_questions)
            }

    def _check_and_create_backup(self) -> Optional[Dict[str, Any]]:
        """
        检查并创建备份

        Returns:
            备份信息
        """
        if self.total_saved_count > 0 and self.total_saved_count % self.auto_backup_interval == 0:
            try:
                self.backup_counter += 1
                backup_suffix = f"auto_{self.backup_counter}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 读取主文件内容并创建备份
                if self.main_file_path and os.path.exists(self.main_file_path):
                    # For INI, we don't read the file back to convert. 
                    # Instead, we pass the accumulated questions directly to create backup.
                    backup_questions = self.accumulated_questions # Use accumulated questions for backup
                    backup_path = self.ini_writer.create_progress_backup(backup_questions, backup_suffix)
                    
                    logger.info(f"自动备份创建: {backup_path}")
                    
                    return {
                        'created': True,
                        'backup_path': backup_path,
                        'backup_count': self.backup_counter,
                        'questions_count': len(backup_questions)
                    }
                    
            except Exception as e:
                logger.error(f"创建自动备份失败: {str(e)}")
                return {'created': False, 'error': str(e)}
        
        return None

    def force_save_all(self) -> Dict[str, Any]:
        """
        强制保存所有累计的题目

        Returns:
            保存结果
        """
        logger.info("强制保存所有累计题目...")
        return self._save_accumulated_questions()

    def get_status(self) -> Dict[str, Any]:
        """
        获取当前状态

        Returns:
            状态信息
        """
        return {
            'accumulated_count': len(self.accumulated_questions),
            'current_batch_count': self.current_batch_count,
            'total_saved_count': self.total_saved_count,
            'batch_save_threshold': self.batch_save_count,
            'auto_backup_interval': self.auto_backup_interval,
            'main_file_path': self.main_file_path,
            'last_save_time': self.last_save_time.strftime("%Y-%m-%d %H:%M:%S") if self.last_save_time else None,
            'backup_counter': self.backup_counter,
            'next_save_at': self.batch_save_count - self.current_batch_count,
            'next_backup_at': self.auto_backup_interval - (self.total_saved_count % self.auto_backup_interval) if self.total_saved_count > 0 else self.auto_backup_interval
        }

    def cleanup_old_backups(self, keep_count: int = 5) -> Dict[str, Any]:
        """
        清理旧的备份文件，只保留最新的几个

        Args:
            keep_count: 保留的备份数量

        Returns:
            清理结果
        """
        try:
            backup_dir = self.ini_writer.output_dir
            backup_files = []
            
            # 查找备份文件
            for filename in os.listdir(backup_dir):
                if filename.startswith('quiz_backup_auto_') and filename.endswith('.ini'):
                    file_path = os.path.join(backup_dir, filename)
                    file_time = os.path.getmtime(file_path)
                    backup_files.append((file_path, file_time, filename))
            
            # 按时间排序，保留最新的
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            deleted_count = 0
            if len(backup_files) > keep_count:
                for file_path, _, filename in backup_files[keep_count:]:
                    try:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除旧备份: {filename}")
                    except Exception as e:
                        logger.error(f"删除备份文件失败 {filename}: {e}")
            
            return {
                'success': True,
                'total_backups': len(backup_files),
                'deleted_count': deleted_count,
                'kept_count': min(len(backup_files), keep_count)
            }
            
        except Exception as e:
            logger.error(f"清理备份文件失败: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_progress_info(self) -> str:
        """
        获取进度信息字符串

        Returns:
            进度信息
        """
        status = self.get_status()
        
        progress_text = f"累计题目: {status['accumulated_count']}"
        progress_text += f" | 已保存: {status['total_saved_count']}"
        progress_text += f" | 距离下次保存: {status['next_save_at']}"
        
        if status['main_file_path']:
            progress_text += f" | 文件: {os.path.basename(status['main_file_path'])}"
        
        return progress_text

    def reset(self):
        """重置状态"""
        self.accumulated_questions.clear()
        self.total_saved_count = 0
        self.current_batch_count = 0
        self.main_file_path = None
        self.backup_counter = 0
        self.last_save_time = datetime.now()
        
        logger.info("增量保存管理器状态已重置")

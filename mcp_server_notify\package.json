{"name": "mcp_server_notify", "version": "1.0.0", "description": "基于Model Context Protocol (MCP)的通知服务器，为LLM提供系统通知功能", "main": "index.js", "bin": {"mcp-notify": "./bin/mcp-notify"}, "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mcp", "notification", "llm", "ai", "desktop-notification"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "node-notifier": "^10.0.1", "socket.io": "^4.7.2", "socket.io-client": "^4.7.2"}}
import pandas as pd

input_file = "import_question_template.csv"
output_file = "cleaned_question_template.csv"

try:
    # 尝试使用 GBK 编码读取文件
    df = pd.read_csv(input_file, encoding="gbk")
    # 将数据写入新的 UTF-8 编码的 CSV 文件
    df.to_csv(output_file, encoding="utf-8", index=False)
    print(f"文件已成功从 GBK 编码转换为 UTF-8 并保存为 {output_file}")
except UnicodeDecodeError:
    print("使用 GBK 编码读取文件失败，尝试使用 UTF-8 编码。")
    try:
        # 如果 GBK 失败，尝试使用 UTF-8 编码读取文件
        df = pd.read_csv(input_file, encoding="utf-8")
        # 将数据写入新的 UTF-8 编码的 CSV 文件
        df.to_csv(output_file, encoding="utf-8", index=False)
        print(f"文件已成功从 UTF-8 编码（假设）转换为 UTF-8 并保存为 {output_file}")
    except Exception as e:
        print(f"读取或写入文件时发生错误: {e}")
except Exception as e:
    print(f"处理文件时发生错误: {e}") 
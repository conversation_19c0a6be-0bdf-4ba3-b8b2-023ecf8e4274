#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用的dummy.docx文件
"""

from docx import Document
import os

def create_dummy_docx():
    """创建测试用的docx文件"""
    doc = Document()
    
    # 添加标题
    doc.add_heading('测试文档', 0)
    
    # 添加目录
    doc.add_heading('目录', level=1)
    doc.add_paragraph('第一章 概述 ........................... 1')
    doc.add_paragraph('第二章 详细内容 ..................... 2')
    doc.add_paragraph('第三章 总结 ......................... 3')
    
    # 添加正文内容
    doc.add_heading('第一章 概述', level=1)
    doc.add_paragraph('这是第一章的内容。本章主要介绍了项目的基本概念和背景信息。')
    doc.add_paragraph('项目的目标是实现自动化的题库生成功能，提高工作效率。')
    
    doc.add_heading('第二章 详细内容', level=1)
    doc.add_paragraph('这是第二章的详细内容。本章深入讨论了技术实现方案。')
    doc.add_paragraph('系统采用了先进的自然语言处理技术，能够从文档中自动提取关键信息。')
    doc.add_paragraph('生成的题目包括单选题、多选题、填空题、简答题和判断题等多种类型。')
    
    doc.add_heading('第三章 总结', level=1)
    doc.add_paragraph('本文档总结了项目的主要成果和未来发展方向。')
    doc.add_paragraph('通过实施本项目，可以显著提高题库建设的效率和质量。')
    
    # 保存文件
    file_path = os.path.join(os.path.dirname(__file__), 'dummy.docx')
    doc.save(file_path)
    print(f"已创建测试文件: {file_path}")
    return file_path

if __name__ == '__main__':
    create_dummy_docx()

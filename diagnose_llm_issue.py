"""
诊断LLM生成失败问题
"""

import os
import sys
import requests
import json
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config

def read_document_content():
    """读取文档内容"""
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    try:
        doc = Document(doc_path)
        content_parts = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                content_parts.append(text)
        
        for table in doc.tables:
            for row in table.rows:
                row_content = []
                for cell in row.cells:
                    row_content.append(cell.text.strip())
                if any(row_content):
                    content_parts.append(' | '.join(row_content))
        
        content = '\n\n'.join(content_parts)
        return content
        
    except Exception as e:
        print(f"[错误] 读取文档失败: {str(e)}")
        return None

def test_chunk_sizes(content):
    """测试不同的内容块大小"""
    print("=" * 60)
    print("测试不同内容块大小")
    print("=" * 60)
    
    if not content:
        print("[错误] 没有内容可供测试")
        return
    
    # 测试不同的块大小
    chunk_sizes = [1000, 2000, 3000, 5000, 10000]
    
    for chunk_size in chunk_sizes:
        chunk = content[:chunk_size]
        print(f"\n[测试] 块大小: {chunk_size} 字符")
        print(f"  实际长度: {len(chunk)} 字符")
        print(f"  词汇数: {len(chunk.split())} 个")
        
        # 检查内容质量
        if '规定' in chunk or '要求' in chunk:
            print(f"  [OK] 包含规范性语言")
        else:
            print(f"  [警告] 缺少规范性语言")
            
        if '输煤' in chunk or '机组' in chunk:
            print(f"  [OK] 包含专业术语")
        else:
            print(f"  [警告] 缺少专业术语")
        
        # 测试LLM生成
        success = test_llm_with_chunk(chunk, chunk_size)
        if success:
            print(f"  [成功] LLM生成成功")
            break
        else:
            print(f"  [失败] LLM生成失败")

def test_llm_with_chunk(content, chunk_size):
    """使用特定内容块测试LLM"""
    api_url = "http://10.45.131.70:3001/v1/chat/completions"
    api_key = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    model = "rsv-rcgjfz7v"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 简化的提示词
    prompt = f"""请根据以下文档内容生成1道单选题。

**要求**：
- 严格基于文档内容
- 如果内容不足以生成题目，请返回"insufficient_content"

**输出格式**：
1.【单选题】题目内容
A、选项内容
B、选项内容
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：解析内容
知识点：知识点1

文档内容：
{content}
"""
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "你是一个专业的题目生成助手。请严格按照要求输出。"
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "max_tokens": 1000,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(api_url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            response_json = response.json()
            content_response = response_json['choices'][0]['message']['content']
            
            print(f"    API响应长度: {len(content_response)} 字符")
            
            if "insufficient_content" in content_response.lower():
                print(f"    [失败] LLM报告内容不足")
                return False
            elif "【单选题】" in content_response:
                print(f"    [成功] LLM生成了题目")
                print(f"    响应预览: {content_response[:200]}...")
                return True
            else:
                print(f"    [失败] LLM响应格式异常")
                print(f"    响应预览: {content_response[:200]}...")
                return False
        else:
            print(f"    [错误] API调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    [错误] API调用异常: {str(e)}")
        return False

def test_specific_sections(content):
    """测试文档的特定章节"""
    print("\n" + "=" * 60)
    print("测试文档特定章节")
    print("=" * 60)
    
    # 按章节分割内容
    sections = content.split('\n\n')
    
    # 找到包含实质内容的章节
    substantial_sections = []
    for i, section in enumerate(sections):
        if len(section) > 200 and ('规定' in section or '要求' in section or '应当' in section):
            substantial_sections.append((i, section))
    
    print(f"[信息] 找到 {len(substantial_sections)} 个实质性章节")
    
    # 测试前几个章节
    for i, (section_idx, section) in enumerate(substantial_sections[:3]):
        print(f"\n[测试] 章节 {section_idx+1} (长度: {len(section)} 字符)")
        print(f"  内容预览: {section[:100]}...")
        
        success = test_llm_with_chunk(section, len(section))
        if success:
            print(f"  [成功] 该章节可以生成题目")
            return section
        else:
            print(f"  [失败] 该章节无法生成题目")
    
    return None

def analyze_prompt_issues():
    """分析提示词可能的问题"""
    print("\n" + "=" * 60)
    print("分析提示词问题")
    print("=" * 60)
    
    # 从配置文件加载配置
    config = Config.from_json_file("test_config.json")
    
    print(f"[配置] 当前配置:")
    print(f"  单选题数量: {config.SINGLE_CHOICE_COUNT}")
    print(f"  多选题数量: {config.MULTIPLE_CHOICE_COUNT}")
    print(f"  填空题数量: {config.FILL_BLANK_COUNT}")
    print(f"  简答题数量: {config.SHORT_ANSWER_COUNT}")
    print(f"  判断题数量: {config.TRUE_FALSE_COUNT}")
    print(f"  总题目数量: {config.SINGLE_CHOICE_COUNT + config.MULTIPLE_CHOICE_COUNT + config.FILL_BLANK_COUNT + config.SHORT_ANSWER_COUNT + config.TRUE_FALSE_COUNT}")
    
    total_questions = (config.SINGLE_CHOICE_COUNT + config.MULTIPLE_CHOICE_COUNT + 
                      config.FILL_BLANK_COUNT + config.SHORT_ANSWER_COUNT + config.TRUE_FALSE_COUNT)
    
    if total_questions > 6:
        print(f"  [警告] 要求生成的题目数量较多 ({total_questions}道)，可能导致LLM认为内容不足")
        print(f"  [建议] 尝试减少题目数量")
    else:
        print(f"  [OK] 题目数量合理")

def main():
    """主函数"""
    print("[诊断] 660MW超临界机组输煤运行规程 - LLM生成失败问题诊断")
    
    # 步骤1: 读取文档
    content = read_document_content()
    
    if content:
        print(f"[信息] 文档总长度: {len(content)} 字符")
        
        # 步骤2: 分析配置问题
        analyze_prompt_issues()
        
        # 步骤3: 测试不同块大小
        test_chunk_sizes(content)
        
        # 步骤4: 测试特定章节
        working_section = test_specific_sections(content)
        
        if working_section:
            print(f"\n[发现] 找到可以生成题目的章节")
            print(f"[建议] 可以使用该章节内容进行题目生成")
        else:
            print(f"\n[结论] 所有测试的章节都无法生成题目")
            print(f"[建议] 可能需要:")
            print(f"  1. 调整LLM提示词")
            print(f"  2. 减少要求的题目数量")
            print(f"  3. 检查API配置")
            print(f"  4. 尝试不同的内容提取方式")
        
        print(f"\n[完成] 诊断完成")
    else:
        print(f"[错误] 无法读取文档")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 诊断程序异常: {str(e)}")
        import traceback
        traceback.print_exc()

"""
修复文档分块问题的脚本
增加分块大小以确保LLM能够生成题目
"""

import os
import sys
import json
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from document_processor.doc_reader import DocumentReader
from document_processor.text_splitter import TextSplitter
from llm_service.openai_client import OpenAICompatibleClient
from output_manager.ini_writer import INIWriter

def create_optimized_config():
    """创建优化的配置"""
    print("=" * 60)
    print("创建优化配置")
    print("=" * 60)
    
    # 基于测试结果，我们知道3000字符可以成功生成题目
    # 为了保险起见，我们增加到5000字符
    optimized_config = {
        "api": {
            "base_url": "http://************:3001",
            "key": "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3",
            "model": "rsv-rcgjfz7v",
            "max_tokens": 4000,
            "temperature": 0.7,
            "timeout": 60,
            "max_retries": 3
        },
        "processing": {
            "max_chunk_size": 5000,  # 增加到5000字符
            "questions_per_chunk": 4,  # 减少每块的题目数量
            "single_choice_count": 2,
            "multiple_choice_count": 1,
            "fill_blank_count": 0,  # 暂时禁用填空题
            "short_answer_count": 1,
            "true_false_count": 0,  # 暂时禁用判断题
            "sorting_count": 0,
            "disable_document_splitting": false,
            "enable_chunk_merging": true
        },
        "file_types": {
            "enable_pdf": true,
            "enable_docx": true,
            "enable_md": true,
            "enable_txt": true
        },
        "filtering": {
            "enable_question_filtering": true,
            "filter_keywords": ["错误处理摘要"]
        },
        "output": {
            "dir": "test_output",
            "error_dir": "error_docs",
            "csv_filename": "quiz_results.csv",
            "save_interval": 10,
            "csv_batch_save_count": 20
        }
    }
    
    # 保存优化配置
    config_file = "optimized_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(optimized_config, f, ensure_ascii=False, indent=2)
    
    print(f"[成功] 优化配置已保存: {config_file}")
    print(f"[变更] 主要优化:")
    print(f"  - 分块大小: 3000 -> 5000 字符")
    print(f"  - 每块题目数: 6 -> 4 道")
    print(f"  - 暂时禁用填空题和判断题")
    print(f"  - 输出目录: test_output")
    
    return config_file

def test_with_optimized_config(config_file):
    """使用优化配置测试"""
    print("\n" + "=" * 60)
    print("使用优化配置测试")
    print("=" * 60)
    
    # 文档路径
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    # 加载优化配置
    config = Config.from_json_file(config_file)
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建组件
    doc_reader = DocumentReader()
    text_splitter = TextSplitter(
        max_chunk_size=config.MAX_CHUNK_SIZE,
        disable_splitting=config.DISABLE_DOCUMENT_SPLITTING,
        enable_chunk_merging=config.ENABLE_CHUNK_MERGING
    )
    llm_client = OpenAICompatibleClient(config)
    ini_writer = INIWriter(config)
    
    try:
        # 步骤1: 读取文档
        print("[步骤1] 读取文档...")
        doc_info = doc_reader.read_document(doc_path)
        print(f"  文档长度: {len(doc_info['content'])} 字符")
        
        # 步骤2: 分割文档
        print("[步骤2] 分割文档...")
        chunks = text_splitter.split_document(doc_info)
        print(f"  分割后块数: {len(chunks)}")
        
        for i, chunk in enumerate(chunks[:3]):  # 只显示前3个块
            print(f"    块{i+1}: {chunk['char_count']} 字符")
        
        # 步骤3: 测试LLM生成
        print("[步骤3] 测试LLM生成...")
        successful_chunks = []
        failed_chunks = []
        
        for i, chunk in enumerate(chunks[:3]):  # 只测试前3个块
            print(f"\n  测试块 {i+1}/{min(3, len(chunks))}:")
            print(f"    字符数: {chunk['char_count']}")
            
            try:
                quiz_data = llm_client.generate_quiz(
                    content=chunk['content'],
                    source_filename=chunk['filename']
                )
                
                if quiz_data and 'questions' in quiz_data:
                    questions = quiz_data['questions']
                    print(f"    [成功] 生成 {len(questions)} 道题目")
                    successful_chunks.append((i, chunk, questions))
                    
                    # 显示题目概览
                    for j, question in enumerate(questions, 1):
                        print(f"      题目{j}: 【{question.get('type', '未知')}】{question.get('question', '')[:40]}...")
                        
                elif quiz_data and quiz_data.get('insufficient_content'):
                    print(f"    [失败] 内容不足")
                    failed_chunks.append((i, chunk, 'insufficient_content'))
                else:
                    print(f"    [失败] 未知错误")
                    failed_chunks.append((i, chunk, 'unknown_error'))
                    
            except Exception as e:
                print(f"    [错误] 异常: {str(e)}")
                failed_chunks.append((i, chunk, f'exception: {str(e)}'))
        
        # 步骤4: 保存成功的结果
        if successful_chunks:
            print(f"\n[步骤4] 保存结果...")
            all_questions = []
            for _, _, questions in successful_chunks:
                all_questions.extend(questions)
            
            ini_file = ini_writer.save_questions_to_ini(all_questions, "输煤运行规程_优化测试.ini")
            print(f"  [成功] 保存 {len(all_questions)} 道题目到: {ini_file}")
            
            # 生成统计报告
            summary = ini_writer.generate_summary_report(all_questions)
            print(f"\n[统计] 题目分布:")
            for key, value in summary.items():
                if key != 'total_questions':
                    print(f"    {key}: {value} 道")
        
        # 总结结果
        print(f"\n[总结] 测试结果:")
        print(f"  成功块数: {len(successful_chunks)}")
        print(f"  失败块数: {len(failed_chunks)}")
        
        if len(successful_chunks) > 0:
            print(f"  [成功] 优化配置有效！")
            return True
        else:
            print(f"  [失败] 优化配置仍然无效")
            return False
            
    except Exception as e:
        print(f"[错误] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def provide_final_recommendations(success):
    """提供最终建议"""
    print("\n" + "=" * 60)
    print("最终建议")
    print("=" * 60)
    
    if success:
        print("[成功] 问题已解决！建议:")
        print("1. 使用优化后的配置文件 (optimized_config.json)")
        print("2. 分块大小设置为5000字符")
        print("3. 每块生成4道题目")
        print("4. 优先生成单选题和简答题")
        print("5. 可以逐步增加其他题型")
        
        print("\n[使用方法]:")
        print("  python main.py --config optimized_config.json --input test")
        
    else:
        print("[失败] 问题仍未解决，进一步建议:")
        print("1. 检查API连接和配置")
        print("2. 尝试更大的分块大小 (8000-10000字符)")
        print("3. 进一步减少每块的题目数量 (2-3道)")
        print("4. 检查文档内容的具体格式")
        print("5. 考虑手动选择文档的特定章节")

def main():
    """主函数"""
    print("[修复] 660MW超临界机组输煤运行规程 - 分块问题修复")
    
    # 步骤1: 创建优化配置
    config_file = create_optimized_config()
    
    # 步骤2: 使用优化配置测试
    success = test_with_optimized_config(config_file)
    
    # 步骤3: 提供最终建议
    provide_final_recommendations(success)
    
    print(f"\n[完成] 修复流程完成")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 修复程序异常: {str(e)}")
        import traceback
        traceback.print_exc()

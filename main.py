#!/usr/bin/env python3
"""
文档到题库转换工具主程序
支持DOC/DOCX文档，调用OpenAI兼容API生成题库，输出CSV格式
"""

import os
import sys
import logging
import tkinter as tk
from gui.main_application import MainApplication

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info("正在启动 GUI 应用程序...")

    root = tk.Tk()
    app = MainApplication(root)
    root.mainloop()

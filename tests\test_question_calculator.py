import unittest
from utils.question_calculator import QuestionCalculator

class MockConfig:
    def __init__(self):
        self.BASE_CHAR_THRESHOLD = 500  # 基础字符阈值
        self.BASE_SINGLE_CHOICE_COUNT = 2  # 基础单选题数量
        self.BASE_MULTIPLE_CHOICE_COUNT = 2  # 基础多选题数量
        self.BASE_FILL_BLANK_COUNT = 1  # 基础填空题数量
        self.BASE_SHORT_ANSWER_COUNT = 1  # 基础问答题数量
        self.BASE_TRUE_FALSE_COUNT = 1  # 基础判断题数量
        self.BASE_SORTING_COUNT = 0  # 基础排序题数量

class TestQuestionCalculator(unittest.TestCase):
    def setUp(self):
        self.config = MockConfig()
        self.calculator = QuestionCalculator(self.config)

    def test_small_text(self):
        """测试短文本(低于阈值)的题目数量计算"""
        char_count = 300  # 小于基础阈值500
        counts = self.calculator.calculate_question_counts(char_count)
        
        # 验证总数量符合基础配置
        total = sum(counts.values())
        expected_total = (
            self.config.BASE_SINGLE_CHOICE_COUNT +
            self.config.BASE_MULTIPLE_CHOICE_COUNT +
            self.config.BASE_FILL_BLANK_COUNT +
            self.config.BASE_SHORT_ANSWER_COUNT +
            self.config.BASE_TRUE_FALSE_COUNT
        )
        self.assertEqual(total, expected_total)
        
        # 验证各题型数量
        self.assertEqual(counts['单选题'], self.config.BASE_SINGLE_CHOICE_COUNT)
        self.assertEqual(counts['多选题'], self.config.BASE_MULTIPLE_CHOICE_COUNT)

    def test_large_text(self):
        """测试长文本(超过阈值)的题目数量计算"""
        char_count = 2000  # 是基础阈值的4倍
        counts = self.calculator.calculate_question_counts(char_count)
        
        # 验证数量增加但比例保持
        self.assertGreater(counts['单选题'], self.config.BASE_SINGLE_CHOICE_COUNT)
        self.assertGreater(counts['多选题'], self.config.BASE_MULTIPLE_CHOICE_COUNT)
        
        # 验证比例关系
        ratio = counts['单选题'] / counts['多选题']
        base_ratio = (
            self.config.BASE_SINGLE_CHOICE_COUNT / 
            self.config.BASE_MULTIPLE_CHOICE_COUNT
        )
        self.assertAlmostEqual(ratio, base_ratio, delta=0.5)

    def test_empty_text(self):
        """测试空文本输入"""
        counts = self.calculator.calculate_question_counts(0)
        
        # 每种题型至少1题
        self.assertGreaterEqual(counts['单选题'], 1)
        self.assertGreaterEqual(counts['多选题'], 1)
        self.assertGreaterEqual(counts['判断题'], 1)

    def test_chunk_processing(self):
        """测试文本块处理方法"""
        test_chunk = {'content': '这是一个500字符的文本块' + 'a' * 480}
        counts = self.calculator.get_question_counts_for_chunk(test_chunk)
        
        # 验证字符数计算正确
        self.assertEqual(sum(counts.values()), 7)  # 基础总数2+2+1+1+1=7

if __name__ == '__main__':
    unittest.main()
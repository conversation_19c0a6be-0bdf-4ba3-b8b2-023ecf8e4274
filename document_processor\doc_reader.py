"""
文档读取模块
支持DOCX、Markdown和TXT格式文件的读取和内容提取
"""

import os
import logging
import re
from typing import Dict, List
from docx import Document

logger = logging.getLogger(__name__)

class DocumentReader:
    """文档读取器，支持DOCX、Markdown和TXT格式"""

    def __init__(self, enabled_formats=None):
        self.all_supported_formats = ['.docx', '.md', '.txt']
        # 如果指定了启用的格式，使用指定的；否则使用所有支持的格式
        self.supported_formats = enabled_formats if enabled_formats else self.all_supported_formats
        self.use_detailed_docx_parsing = True  # 是否对DOCX使用详细解析

    def read_document(self, file_path: str) -> Dict[str, any]:
        """
        读取文档内容

        Args:
            file_path: 文档文件路径

        Returns:
            包含文档信息的字典：{
                'filename': 文件名,
                'content': 文档内容,
                'structure': 文档结构信息,
                'metadata': 元数据
            }
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_ext}")

        try:
            if file_ext == '.docx':
                return self._read_docx(file_path)
            elif file_ext == '.md':
                return self._read_markdown(file_path)
            elif file_ext == '.txt':
                return self._read_txt(file_path)
        except Exception as e:
            logger.error(f"读取文档失败 {file_path}: {str(e)}")
            raise

    def _read_docx(self, file_path: str) -> Dict[str, any]:
        """读取DOCX文件"""
        try:
            if self.use_detailed_docx_parsing:
                # 使用详细的python-docx解析
                return self._read_docx_detailed(file_path)
            else:
                # 使用textract简单提取
                return self._read_docx_simple(file_path)
        except Exception as e:
            logger.warning(f"详细DOCX解析失败，尝试textract: {str(e)}")
            # 如果详细解析失败，尝试textract
            return self._read_docx_simple(file_path)

    def _read_docx_detailed(self, file_path: str) -> Dict[str, any]:
        """使用python-docx进行详细的DOCX文件解析"""
        try:
            doc = Document(file_path)

            content_parts = []
            structure = []
            skip_mode = False  # 是否跳过目录部分

            for i, paragraph in enumerate(doc.paragraphs):
                text = paragraph.text.strip()
                if not text:
                    continue
                    
                # 判断段落类型
                style_name = paragraph.style.name if paragraph.style else "Normal"
                is_heading = 'Heading' in style_name
                
                # 检查是否发现目录部分
                toc_keywords = ['目录', 'Contents', 'Table of Contents']
                is_toc = any(text.strip() == keyword or text.strip().startswith(keyword + ' ') for keyword in toc_keywords)
                if is_heading and is_toc:
                    skip_mode = True
                    continue
                    
                # 跳过目录部分的内容
                if skip_mode:
                    # 如果遇到下一个章节标题(且处于跳过模式)，则停止跳过
                    if is_heading:
                        skip_mode = False
                        # 不跳过这个标题，继续处理
                    else:
                        # 跳过目录内容
                        continue

                structure.append({
                    'index': i,
                    'type': 'paragraph',
                    'style': style_name,
                    'text': text,
                    'is_heading': is_heading
                })

                content_parts.append(text)

            # 处理表格
            for table_idx, table in enumerate(doc.tables):
                table_content = []
                for row in table.rows:
                    row_content = []
                    for cell in row.cells:
                        row_content.append(cell.text.strip())
                    table_content.append(row_content)

                structure.append({
                    'type': 'table',
                    'index': table_idx,
                    'content': table_content
                })

                # 将表格内容添加到文本中
                for row in table_content:
                    content_parts.append(' | '.join(row))

            return {
                'filename': os.path.basename(file_path),
                'content': '\n\n'.join(content_parts),
                'structure': structure,
                'metadata': {
                    'paragraphs_count': len(doc.paragraphs),
                    'tables_count': len(doc.tables)
                }
            }

        except Exception as e:
            logger.error(f"读取DOCX文件失败 {file_path}: {str(e)}")
            raise

    def _read_docx_simple(self, file_path: str) -> Dict[str, any]:
        """使用python-docx进行简单的DOCX文件解析"""
        try:
            doc = Document(file_path)
            content = '\n'.join([paragraph.text for paragraph in doc.paragraphs])

            return {
                'filename': os.path.basename(file_path),
                'content': content,
                'structure': [{'type': 'text', 'content': content}],
                'metadata': {
                    'extraction_method': 'python-docx',
                    'file_type': 'docx'
                }
            }

        except Exception as e:
            logger.error(f"读取DOCX文件失败 {file_path}: {str(e)}")
            raise



    def batch_read_documents(self, directory: str, recursive: bool = True) -> List[Dict[str, any]]:
        """
        批量读取目录中的文档

        Args:
            directory: 文档目录路径
            recursive: 是否递归读取子文件夹，默认为True

        Returns:
            文档信息列表
        """
        documents = []

        if not os.path.exists(directory):
            raise FileNotFoundError(f"目录不存在: {directory}")

        if not os.path.isdir(directory):
            raise ValueError(f"路径不是目录: {directory}")

        # 获取所有支持的文件路径
        file_paths = self._scan_directory_recursive(directory, recursive)

        logger.info(f"发现 {len(file_paths)} 个支持的文档文件")

        for file_path in file_paths:
            try:
                doc_info = self.read_document(file_path)

                # 添加相对路径信息，便于组织文件结构
                relative_path = os.path.relpath(file_path, directory)
                doc_info['relative_path'] = relative_path
                doc_info['directory_depth'] = len(os.path.dirname(relative_path).split(os.sep)) if os.path.dirname(relative_path) else 0

                documents.append(doc_info)
                logger.info(f"成功读取文档: {relative_path}")
            except Exception as e:
                logger.error(f"读取文档失败 {os.path.relpath(file_path, directory)}: {str(e)}")
                # 继续处理其他文档
                continue

        logger.info(f"批量读取完成，共处理 {len(documents)} 个文档")
        return documents

    def _scan_directory_recursive(self, directory: str, recursive: bool = True) -> List[str]:
        """
        递归扫描目录，获取所有支持的文档文件路径

        Args:
            directory: 要扫描的目录
            recursive: 是否递归扫描子目录

        Returns:
            支持的文档文件路径列表
        """
        file_paths = []

        try:
            if recursive:
                # 递归遍历所有子目录
                for root, _, files in os.walk(directory):
                    for filename in files:
                        file_path = os.path.join(root, filename)
                        file_ext = os.path.splitext(filename)[1].lower()

                        if file_ext in self.supported_formats:
                            file_paths.append(file_path)
            else:
                # 只扫描当前目录
                for filename in os.listdir(directory):
                    file_path = os.path.join(directory, filename)

                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(filename)[1].lower()

                        if file_ext in self.supported_formats:
                            file_paths.append(file_path)

        except Exception as e:
            logger.error(f"扫描目录失败 {directory}: {str(e)}")
            raise

        # 按文件路径排序，确保处理顺序一致
        file_paths.sort()
        return file_paths



    def _read_markdown(self, file_path: str) -> Dict[str, any]:
        """
        读取Markdown文件
        解析Markdown结构，包括标题、段落、列表等
        """
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 如果文件为空
            if not content.strip():
                return {
                    'filename': os.path.basename(file_path),
                    'content': '',
                    'structure': [],
                    'metadata': {
                        'file_type': 'markdown',
                        'has_front_matter': False
                    }
                }

            # 解析YAML front matter（如果存在）
            front_matter = {}
            has_front_matter = False

            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    try:
                        # 简单解析YAML front matter
                        front_matter_text = parts[1].strip()
                        for line in front_matter_text.split('\n'):
                            if ':' in line:
                                key, value = line.split(':', 1)
                                front_matter[key.strip()] = value.strip()
                        content = parts[2].strip()
                        has_front_matter = True
                    except:
                        # 如果解析失败，保持原内容
                        pass

            # 解析Markdown结构
            structure = self._parse_markdown_structure(content)

            # 清理内容（移除Markdown标记，保留纯文本）
            clean_content = self._clean_markdown_content(content)

            return {
                'filename': os.path.basename(file_path),
                'content': clean_content,
                'structure': structure,
                'metadata': {
                    'file_type': 'markdown',
                    'has_front_matter': has_front_matter,
                    'front_matter': front_matter,
                    'original_content': content  # 保留原始Markdown内容
                }
            }

        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                # 递归调用，但使用已读取的内容
                structure = self._parse_markdown_structure(content)
                clean_content = self._clean_markdown_content(content)
                return {
                    'filename': os.path.basename(file_path),
                    'content': clean_content,
                    'structure': structure,
                    'metadata': {
                        'file_type': 'markdown',
                        'has_front_matter': False,
                        'front_matter': {},
                        'original_content': content,
                        'encoding': 'gbk'
                    }
                }
            except:
                logger.error(f"无法读取Markdown文件编码 {file_path}")
                raise
        except Exception as e:
            logger.error(f"读取Markdown文件失败 {file_path}: {str(e)}")
            raise

    def _parse_markdown_structure(self, content: str) -> List[Dict[str, any]]:
        """解析Markdown文档结构"""
        structure = []
        lines = content.split('\n')

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 标题
            if line.startswith('#'):
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break

                title = line[level:].strip()
                structure.append({
                    'type': 'heading',
                    'level': level,
                    'text': title,
                    'line_number': i + 1
                })

            # 列表项
            elif line.startswith(('- ', '* ', '+ ')) or re.match(r'^\d+\. ', line):
                list_type = 'ordered' if re.match(r'^\d+\. ', line) else 'unordered'
                text = re.sub(r'^(\d+\. |- |\* |\+ )', '', line)
                structure.append({
                    'type': 'list_item',
                    'list_type': list_type,
                    'text': text,
                    'line_number': i + 1
                })

            # 代码块
            elif line.startswith('```'):
                language = line[3:].strip()
                structure.append({
                    'type': 'code_block',
                    'language': language,
                    'line_number': i + 1
                })

            # 引用
            elif line.startswith('>'):
                text = line[1:].strip()
                structure.append({
                    'type': 'quote',
                    'text': text,
                    'line_number': i + 1
                })

            # 普通段落
            elif line and not line.startswith(('!', '[', '|')):  # 排除图片、链接、表格
                structure.append({
                    'type': 'paragraph',
                    'text': line,
                    'line_number': i + 1
                })

        return structure

    def _clean_markdown_content(self, content: str) -> str:
        """清理Markdown内容，移除标记，保留纯文本"""
        # 移除标题标记
        content = re.sub(r'^#{1,6}\s+', '', content, flags=re.MULTILINE)

        # 移除粗体和斜体标记
        content = re.sub(r'\*\*(.*?)\*\*', r'\1', content)  # 粗体
        content = re.sub(r'\*(.*?)\*', r'\1', content)      # 斜体
        content = re.sub(r'__(.*?)__', r'\1', content)      # 粗体
        content = re.sub(r'_(.*?)_', r'\1', content)        # 斜体

        # 移除代码标记
        content = re.sub(r'`(.*?)`', r'\1', content)        # 行内代码
        content = re.sub(r'^```.*?```', '', content, flags=re.MULTILINE | re.DOTALL)  # 代码块

        # 移除链接标记
        content = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', content)  # [text](url)
        content = re.sub(r'\[([^\]]+)\]\[[^\]]*\]', r'\1', content)  # [text][ref]

        # 移除图片标记
        content = re.sub(r'!\[([^\]]*)\]\([^\)]+\)', r'\1', content)  # ![alt](url)

        # 移除引用标记
        content = re.sub(r'^>\s*', '', content, flags=re.MULTILINE)

        # 移除列表标记
        content = re.sub(r'^[\s]*[-\*\+]\s+', '', content, flags=re.MULTILINE)  # 无序列表
        content = re.sub(r'^[\s]*\d+\.\s+', '', content, flags=re.MULTILINE)    # 有序列表

        # 移除水平线
        content = re.sub(r'^[-\*_]{3,}$', '', content, flags=re.MULTILINE)

        # 清理多余的空行
        content = re.sub(r'\n\s*\n', '\n\n', content)
        content = content.strip()

        return content

    def _read_txt(self, file_path: str) -> Dict[str, any]:
        """
        读取TXT文件
        支持多种编码格式的纯文本文件
        """
        try:
            content = ""
            encoding_used = "utf-8"

            # 尝试不同的编码格式
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'ascii']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    encoding_used = encoding
                    break
                except UnicodeDecodeError:
                    continue
                except Exception:
                    continue

            if not content:
                # 如果所有编码都失败，尝试以二进制模式读取并忽略错误
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    encoding_used = "utf-8 (with errors ignored)"
                except Exception as e:
                    logger.error(f"无法读取TXT文件 {file_path}: {str(e)}")
                    raise

            # 如果文件为空
            if not content.strip():
                return {
                    'filename': os.path.basename(file_path),
                    'content': '',
                    'structure': [],
                    'metadata': {
                        'file_type': 'txt',
                        'encoding': encoding_used,
                        'is_empty': True
                    }
                }

            # 简单的结构分析 - 按段落分割
            structure = []
            paragraphs = content.split('\n\n')

            for i, paragraph in enumerate(paragraphs):
                paragraph = paragraph.strip()
                if paragraph:
                    structure.append({
                        'type': 'paragraph',
                        'content': paragraph,
                        'paragraph_number': i + 1,
                        'line_count': len(paragraph.split('\n'))
                    })

            return {
                'filename': os.path.basename(file_path),
                'content': content,
                'structure': structure,
                'metadata': {
                    'file_type': 'txt',
                    'encoding': encoding_used,
                    'total_lines': len(content.split('\n')),
                    'total_paragraphs': len(structure),
                    'character_count': len(content),
                    'is_empty': False
                }
            }

        except Exception as e:
            logger.error(f"读取TXT文件失败 {file_path}: {str(e)}")
            raise
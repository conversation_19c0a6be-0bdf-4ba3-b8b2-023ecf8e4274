"""
配置管理模块
统一使用.env文件进行配置管理
"""

import os
from typing import List

class Config:
    """配置类，统一使用.env文件管理配置"""

    # OpenAI兼容API配置
    API_BASE_URL = "http://************:3001"
    API_KEY = "sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3"
    ENCODING_NAME = "cl100k_base"
    MODEL_NAME = "rsv-rcgjfz7v"
    MAX_TOKENS = 4000
    TEMPERATURE = 0.3
    REQUEST_TIMEOUT = 60
    MAX_RETRIES = 3

    # 文档处理配置
    MAX_CHUNK_SIZE = 3000
    MIN_TOKENS_FOR_GENERATION = 100
    QUESTIONS_PER_CHUNK = 6
    DISABLE_DOCUMENT_SPLITTING = False
    ENABLE_CHUNK_MERGING = True

    # 新分块逻辑配置
    USE_NEW_SPLITTING_LOGIC = True
    BASE_CHAR_THRESHOLD = 2000
    BASE_SINGLE_CHOICE_COUNT = 2
    BASE_MULTIPLE_CHOICE_COUNT = 1
    BASE_FILL_BLANK_COUNT = 1
    BASE_SHORT_ANSWER_COUNT = 1
    BASE_TRUE_FALSE_COUNT = 1
    BASE_SORTING_COUNT = 0

    # 题型数量配置
    SINGLE_CHOICE_COUNT = 2
    MULTIPLE_CHOICE_COUNT = 1
    FILL_BLANK_COUNT = 1
    SHORT_ANSWER_COUNT = 1
    TRUE_FALSE_COUNT = 1
    SORTING_COUNT = 0

    # 文件类型支持配置
    ENABLE_PDF = False  # 已移除PDF支持
    ENABLE_DOCX = True
    ENABLE_MD = True
    ENABLE_TXT = True

    # 输出配置
    OUTPUT_DIR = "output"
    ERROR_DIR = "error_docs"

    # 过滤配置
    ENABLE_QUESTION_FILTERING = False
    FILTER_KEYWORDS = []

    # 进度保存配置
    PROGRESS_SAVE_INTERVAL = 10
    INI_BATCH_SAVE_COUNT = 30
    AUTO_BACKUP_INTERVAL = 200

    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # 题目生成提示词模板
    QUIZ_PROMPT_TEMPLATE = """
你是一个专业的题目生成专家。请根据以下文档内容生成高质量的题目，严格按照新模板.ini格式要求。

**题目数量要求：**
- 单选题：{single_choice_count}道
- 多选题：{multiple_choice_count}道
- 填空题：{fill_blank_count}道
- 简答题：{short_answer_count}道
- 判断题：{true_false_count}道

**文档来源信息：**
文件名：{source_filename}

**核心生成原则：**
1. **必须基于文档内容**：所有题目必须完全基于提供的文档内容，绝对不能编造
2. **灵活调整数量**：如果文档内容不足，可以适当减少题目数量，但至少要生成1道题目
3. **质量优于数量**：宁可生成较少的高质量题目，也不要生成不准确的题目
4. **多样化题型**：优先生成单选题和判断题，这些相对容易生成且准确性高
5. **具体化内容**：题目应引用文档中的具体内容、数据、规定等

**新模板.ini格式填写规范：**
1. 题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】
2. 题目难度：简单/一般/较难/困难
3. 知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字
4. 单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L；正确答案请填写大写A B C D E F G H I J K L
5. 判断题：正确答案填写"对或错"
6. 填空题：题目中用【】表示一个空，最多支持12个空，每个空可设置3个备选答案，多个备选答案用"/"分隔
7. **简答题特殊要求**：关键词用作系统阅卷使用，最多支持12个关键词；关键词不得相同；**关键词应该是浓缩的核心概念，而不是单个字**
8. 避免特殊字符输入（表情、乱码）
9. 如果填空题和简答题答案中包含"/"，请以{{/}}表示

**简答题关键词生成要求**：
- 每个关键词应该是2-6个字的核心概念或术语
- 为每个关键词提供2-3个备选答案，用"/"分隔
- 关键词应涵盖答案的主要知识点
- 示例格式：关键字1：核心概念/主要内容/重点要素

**输出格式要求**：
请直接输出INI格式的题目，不要使用JSON格式。严格按照以下格式：

1.【单选题】题目内容
A、选项内容
B、选项内容
C、选项内容
D、选项内容
正确答案：A
题目难度：简单
答案解析：根据《XX管理办法》第X条规定...
知识点：知识点1|知识点2

2.【多选题】题目内容
A、选项内容
B、选项内容
C、选项内容
D、选项内容
E、选项内容
正确答案：AB
题目难度：一般
答案解析：依据《XX实施细则》...
知识点：知识点1|知识点2

3.【判断题】题目内容
正确答案：对
题目难度：简单
答案解析：按照《XX标准》要求...
知识点：知识点1

4.【填空题】题目内容第1个空【备选答案1/备选答案2/备选答案3】，第2个空【备选答案4/备选答案5/备选答案6】
题目难度：一般
作答上传图片：否
答案解析：根据《XX规定》...
知识点：知识点1|知识点2

5.【简答题】题目内容
关键字1：核心概念1/主要内容1/重点要素1
关键字2：核心概念2/主要内容2/重点要素2
关键字3：核心概念3/主要内容3/重点要素3
题目难度：较难
作答上传图片：否
答案解析：根据《XX办法》和《XX规定》...
知识点：知识点1|知识点2|知识点3

**生成策略建议：**
- 如果文档内容较少，优先生成单选题和判断题
- 如果文档包含具体数据、规定、流程，可以生成填空题
- 如果文档有完整的概念解释，可以生成简答题
- 多选题需要文档有明确的多个要点或分类

**重要提醒：**
- 必须严格按照INI格式输出
- 每道题目都要有完整的格式要素
- 简答题的关键词必须是2-6个字的核心概念
- 如果实在无法生成足够题目，至少生成1道高质量的单选题

**失败处理**：只有在文档内容完全无法理解或为空时，才返回"insufficient_content"。

---

**文档内容：**
{content}

---

请开始生成题目：
"""

    @classmethod
    def from_env(cls):
        """从.env文件加载配置"""
        config = cls()
        
        # 尝试加载.env文件
        env_file = '.env'
        if os.path.exists(env_file):
            config._load_env_file(env_file)
        
        # 从环境变量加载配置
        config.API_BASE_URL = os.getenv('API_BASE_URL', config.API_BASE_URL)
        config.API_KEY = os.getenv('API_KEY', config.API_KEY)
        config.MODEL_NAME = os.getenv('MODEL_NAME', config.MODEL_NAME)
        config.MAX_TOKENS = int(os.getenv('MAX_TOKENS', str(config.MAX_TOKENS)))
        config.TEMPERATURE = float(os.getenv('TEMPERATURE', str(config.TEMPERATURE)))
        config.REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', str(config.REQUEST_TIMEOUT)))
        config.MAX_RETRIES = int(os.getenv('MAX_RETRIES', str(config.MAX_RETRIES)))
        config.MAX_CHUNK_SIZE = int(os.getenv('MAX_CHUNK_SIZE', str(config.MAX_CHUNK_SIZE)))
        config.MIN_TOKENS_FOR_GENERATION = int(os.getenv('MIN_TOKENS_FOR_GENERATION', str(config.MIN_TOKENS_FOR_GENERATION)))
        config.QUESTIONS_PER_CHUNK = int(os.getenv('QUESTIONS_PER_CHUNK', str(config.QUESTIONS_PER_CHUNK)))
        config.DISABLE_DOCUMENT_SPLITTING = os.getenv('DISABLE_DOCUMENT_SPLITTING', 'False').lower() == 'true'
        config.ENABLE_CHUNK_MERGING = os.getenv('ENABLE_CHUNK_MERGING', 'True').lower() == 'true'
        config.OUTPUT_DIR = os.getenv('OUTPUT_DIR', config.OUTPUT_DIR)
        config.ERROR_DIR = os.getenv('ERROR_DIR', config.ERROR_DIR)
        
        # 题型数量配置
        config.SINGLE_CHOICE_COUNT = int(os.getenv('SINGLE_CHOICE_COUNT', str(config.SINGLE_CHOICE_COUNT)))
        config.MULTIPLE_CHOICE_COUNT = int(os.getenv('MULTIPLE_CHOICE_COUNT', str(config.MULTIPLE_CHOICE_COUNT)))
        config.FILL_BLANK_COUNT = int(os.getenv('FILL_BLANK_COUNT', str(config.FILL_BLANK_COUNT)))
        config.SHORT_ANSWER_COUNT = int(os.getenv('SHORT_ANSWER_COUNT', str(config.SHORT_ANSWER_COUNT)))
        config.TRUE_FALSE_COUNT = int(os.getenv('TRUE_FALSE_COUNT', str(config.TRUE_FALSE_COUNT)))
        config.SORTING_COUNT = int(os.getenv('SORTING_COUNT', str(config.SORTING_COUNT)))
        
        # INI保存批次计数
        config.INI_BATCH_SAVE_COUNT = int(os.getenv('INI_BATCH_SAVE_COUNT', str(config.INI_BATCH_SAVE_COUNT)))

        return config

    def _load_env_file(self, env_file: str):
        """加载.env文件到环境变量"""
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"').strip("'")  # 移除引号
                        os.environ[key] = value
        except Exception as e:
            print(f"加载.env文件失败: {e}")

    def is_api_configured(self) -> bool:
        """检查API是否已配置"""
        return bool(self.API_KEY and self.API_KEY != "")

    def get_missing_config_items(self) -> List[str]:
        """获取缺失的配置项"""
        missing = []
        if not self.API_KEY or self.API_KEY == "":
            missing.append("API密钥")
        if not self.API_BASE_URL:
            missing.append("API基础URL")
        if not self.MODEL_NAME:
            missing.append("模型名称")
        return missing

    def get_enabled_formats(self) -> List[str]:
        """获取启用的文件格式列表"""
        enabled_formats = []
        if self.ENABLE_DOCX:
            enabled_formats.append('.docx')
        if self.ENABLE_MD:
            enabled_formats.append('.md')
        if self.ENABLE_TXT:
            enabled_formats.append('.txt')
        return enabled_formats

    def create_default_env_file(self) -> str:
        """创建默认.env文件"""
        env_content = """# 题库生成工具环境配置文件

# API配置
API_BASE_URL=http://************:3001
API_KEY=sk-A4m4GAvEAdxJufNmNnmLyvRGWRFcgnHJfisguSqbwHmNfFM3
MODEL_NAME=rsv-rcgjfz7v
MAX_TOKENS=4000
TEMPERATURE=0.3
REQUEST_TIMEOUT=60
MAX_RETRIES=3

# 文档处理配置
MAX_CHUNK_SIZE=3000
MIN_TOKENS_FOR_GENERATION=100
QUESTIONS_PER_CHUNK=6
DISABLE_DOCUMENT_SPLITTING=false
ENABLE_CHUNK_MERGING=true

# 题型数量配置
SINGLE_CHOICE_COUNT=2
MULTIPLE_CHOICE_COUNT=1
FILL_BLANK_COUNT=1
SHORT_ANSWER_COUNT=1
TRUE_FALSE_COUNT=1
SORTING_COUNT=0

# 输出配置
OUTPUT_DIR=output
ERROR_DIR=error_docs
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        return '.env'

    @classmethod
    def setup_wizard(cls):
        """配置设置向导"""
        print("=" * 60)
        print("题库生成工具 - 配置设置向导")
        print("=" * 60)

        config = cls()

        # API配置
        print("\n1. API配置")
        print("当前默认配置:")
        print(f"  API地址: {config.API_BASE_URL}")
        print(f"  模型名称: {config.MODEL_NAME}")
        
        use_default = input("\n是否使用默认配置? (y/n, 默认y): ").strip().lower()
        
        if use_default != 'y' and use_default != '':
            config.API_BASE_URL = input("请输入API基础URL: ").strip() or config.API_BASE_URL
            config.API_KEY = input("请输入API密钥: ").strip() or config.API_KEY
            config.MODEL_NAME = input("请输入模型名称: ").strip() or config.MODEL_NAME

        # 处理配置
        print("\n2. 处理配置")
        chunk_size = input(f"文本块大小 (默认 {config.MAX_CHUNK_SIZE}): ").strip()
        if chunk_size:
            config.MAX_CHUNK_SIZE = int(chunk_size)

        questions_per_chunk = input(f"每块题目数量 (默认 {config.QUESTIONS_PER_CHUNK}): ").strip()
        if questions_per_chunk:
            config.QUESTIONS_PER_CHUNK = int(questions_per_chunk)

        # 保存配置
        print("\n3. 保存配置")
        filename = config.create_default_env_file()
        print(f"\n[OK] 配置已保存到: {filename}")
        print("现在可以使用工具了！")

        return config

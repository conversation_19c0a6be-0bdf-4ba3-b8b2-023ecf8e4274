"""
演示实时错误保存功能
模拟真实的文档处理流程，展示当LLM报告内容不足时如何实时保存错误片段
"""

import os
import sys
import time
from pathlib import Path
from docx import Document

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import Config
from document_processor.doc_reader import DocumentReader
from document_processor.text_splitter import TextSplitter
from llm_service.openai_client import OpenAICompatibleClient
from error_handler.error_manager import ErrorManager

def simulate_document_processing():
    """模拟文档处理流程，展示实时错误保存"""
    print("=" * 60)
    print("实时错误保存功能演示")
    print("=" * 60)
    
    # 文档路径
    doc_path = "test/附件5：660MW超临界机组输煤运行规程（2024修订）.docx"
    
    if not os.path.exists(doc_path):
        print(f"[错误] 文档不存在: {doc_path}")
        return False
    
    # 创建配置 - 使用较小的分块大小来模拟内容不足的情况
    config = Config.from_json_file("test_config.json")
    config.OUTPUT_DIR = "demo_output"
    config.ERROR_DIR = "demo_error_docs"
    config.MAX_CHUNK_SIZE = 1000  # 故意设置较小的分块大小
    
    # 确保输出目录存在
    os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    # 创建组件
    doc_reader = DocumentReader()
    text_splitter = TextSplitter(
        max_chunk_size=config.MAX_CHUNK_SIZE,
        disable_splitting=config.DISABLE_DOCUMENT_SPLITTING,
        enable_chunk_merging=config.ENABLE_CHUNK_MERGING
    )
    llm_client = OpenAICompatibleClient(config)
    error_manager = ErrorManager(config)
    
    print(f"[配置] 分块大小: {config.MAX_CHUNK_SIZE} 字符")
    print(f"[配置] 输出目录: {config.OUTPUT_DIR}")
    print(f"[配置] 错误目录: {config.ERROR_DIR}")
    
    try:
        # 步骤1: 读取文档
        print(f"\n[步骤1] 读取文档...")
        doc_info = doc_reader.read_document(doc_path)
        print(f"  文档长度: {len(doc_info['content'])} 字符")
        
        # 步骤2: 分割文档
        print(f"\n[步骤2] 分割文档...")
        chunks = text_splitter.split_document(doc_info)
        print(f"  分割后块数: {len(chunks)}")
        
        # 步骤3: 模拟处理前几个块
        print(f"\n[步骤3] 处理文档块（模拟LLM内容不足的情况）...")
        
        successful_count = 0
        failed_count = 0
        realtime_saved_files = []
        
        # 只处理前5个块进行演示
        for i, chunk in enumerate(chunks[:5]):
            print(f"\n  处理块 {i+1}/{min(5, len(chunks))}:")
            print(f"    文件: {chunk['filename']}")
            print(f"    字符数: {chunk['char_count']}")
            print(f"    内容预览: {chunk['content'][:100]}...")
            
            try:
                # 调用LLM生成题目
                quiz_data = llm_client.generate_quiz(
                    content=chunk['content'],
                    source_filename=chunk['filename']
                )
                
                if quiz_data and 'questions' in quiz_data:
                    questions = quiz_data['questions']
                    print(f"    [成功] 生成 {len(questions)} 道题目")
                    successful_count += 1
                    
                elif quiz_data and quiz_data.get('insufficient_content'):
                    print(f"    [失败] LLM报告内容不足")
                    failed_count += 1
                    
                    # 实时保存失败片段
                    print(f"    [保存] 实时保存失败片段...")
                    saved_file = error_manager.save_failed_chunk_realtime(
                        chunk, 
                        "LLM报告文档内容不足以生成题目"
                    )
                    
                    if saved_file:
                        realtime_saved_files.append(saved_file)
                        print(f"    [成功] 已保存到: {os.path.basename(saved_file)}")
                    else:
                        print(f"    [错误] 保存失败")
                        
                else:
                    print(f"    [失败] 其他错误")
                    failed_count += 1
                    
                    # 也保存其他类型的失败
                    saved_file = error_manager.save_failed_chunk_realtime(
                        chunk, 
                        "LLM生成失败，未知错误"
                    )
                    
                    if saved_file:
                        realtime_saved_files.append(saved_file)
                        print(f"    [保存] 已保存失败片段: {os.path.basename(saved_file)}")
                    
            except Exception as e:
                print(f"    [异常] 处理异常: {str(e)}")
                failed_count += 1
                
                # 保存异常情况
                saved_file = error_manager.save_failed_chunk_realtime(
                    chunk, 
                    f"处理异常: {str(e)}"
                )
                
                if saved_file:
                    realtime_saved_files.append(saved_file)
                    print(f"    [保存] 已保存异常片段: {os.path.basename(saved_file)}")
            
            # 短暂延迟，模拟真实处理
            time.sleep(0.2)
        
        # 步骤4: 显示统计结果
        print(f"\n[步骤4] 处理结果统计:")
        print(f"  成功处理: {successful_count} 个块")
        print(f"  失败处理: {failed_count} 个块")
        print(f"  实时保存: {len(realtime_saved_files)} 个文件")
        
        # 步骤5: 显示实时保存统计
        print(f"\n[步骤5] 实时保存统计:")
        stats = error_manager.get_realtime_save_statistics()
        print(f"  总实时保存次数: {stats['total_realtime_saves']}")
        print(f"  实时保存文件数: {len(stats['realtime_files'])}")
        print(f"  错误文档目录: {stats['err_docs_dir']}")
        
        if stats['realtime_files']:
            print(f"\n[文件列表] 最新的实时保存文件:")
            for file_info in stats['realtime_files'][:3]:  # 显示最新的3个
                print(f"  - {file_info['filename']}")
                print(f"    大小: {file_info['size']} 字节")
                print(f"    时间: {file_info['modified'].strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 步骤6: 展示保存文件的内容
        if realtime_saved_files:
            print(f"\n[步骤6] 展示保存文件内容:")
            first_file = realtime_saved_files[0]
            print(f"  文件: {os.path.basename(first_file)}")
            
            try:
                with open(first_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    print(f"  内容预览（前15行）:")
                    for i, line in enumerate(lines[:15], 1):
                        print(f"    {i:2d}: {line}")
                    if len(lines) > 15:
                        print(f"    ... (共 {len(lines)} 行)")
            except Exception as e:
                print(f"  [错误] 读取文件失败: {str(e)}")
        
        print(f"\n[完成] 演示完成")
        return True
        
    except Exception as e:
        print(f"[错误] 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_benefits():
    """展示实时错误保存的好处"""
    print("\n" + "=" * 60)
    print("实时错误保存功能的好处")
    print("=" * 60)
    
    benefits = [
        "1. 立即保存失败片段，不会丢失有价值的错误信息",
        "2. 每个失败片段都有详细的元数据（文件名、片段索引、字符数等）",
        "3. 包含完整的文档内容，便于后续分析和调试",
        "4. 时间戳精确到毫秒，便于追踪处理顺序",
        "5. 独立的实时保存统计，便于监控系统性能",
        "6. 可以用于分析哪些类型的内容容易导致生成失败",
        "7. 支持手动重新处理保存的失败片段",
        "8. 有助于优化分块策略和LLM提示词"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\n[使用场景]:")
    scenarios = [
        "- 大批量文档处理时，及时发现和保存问题片段",
        "- 系统调试和优化时，分析失败模式",
        "- 质量控制时，检查哪些内容无法生成题目",
        "- 手动干预时，重新处理特定的失败片段"
    ]
    
    for scenario in scenarios:
        print(f"  {scenario}")

def main():
    """主函数"""
    print("[演示] 660MW超临界机组输煤运行规程 - 实时错误保存功能")
    
    # 运行演示
    success = simulate_document_processing()
    
    # 展示功能好处
    show_benefits()
    
    # 总结
    print(f"\n" + "=" * 60)
    print("演示总结")
    print("=" * 60)
    
    if success:
        print("[成功] 实时错误保存功能演示成功")
        print("[说明] 当LLM报告内容不足时，系统会立即保存失败片段")
        print("[位置] 保存的错误片段位于 demo_error_docs/err-docs/ 目录")
        print("[格式] 文件名格式: failed_realtime_[原文件名]_chunk[索引]_[时间戳].txt")
        print("[内容] 包含完整的片段内容和错误信息")
    else:
        print("[失败] 演示过程中出现错误")
        print("[建议] 检查文档路径和配置文件")
    
    print(f"\n[下一步] 可以:")
    print(f"  1. 查看 demo_error_docs/err-docs/ 目录中的保存文件")
    print(f"  2. 分析失败片段的内容特征")
    print(f"  3. 调整分块大小或LLM提示词")
    print(f"  4. 手动重新处理特定的失败片段")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"[错误] 演示程序异常: {str(e)}")
        import traceback
        traceback.print_exc()

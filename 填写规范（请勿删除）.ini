填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用“【】”标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用“|”分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写“对或错”；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用“/”分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；备选答案不能一致
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含“/”，请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案“a”、“b/b”、“c//c”。


1.【单选题】题目生成是本项目的核心功能，主要由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调，并依赖于 [`llm_service/openai_client.py`](llm_service/openai_client.py:1) 和 [`utils/question_calculator.py`](utils/question_calculator.py:1) 完成具体任务。
A、正确
B、错误
正确答案：A
题目难度：简单
答案解析：题目生成是本项目的核心功能，主要由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调，并依赖于 [`llm_service/openai_client.py`](llm_service/openai_client.py:1) 和 [`utils/question_calculator.py`](utils/question_calculator.py:1) 完成具体任务。
知识点：题目生成|LLM交互|题目数量计算


2.【多选题】自动保存功能旨在防止数据丢失，并在处理过程中提供进度管理和结果输出。主要由 [`output_manager/incremental_saver.py`](output_manager/incremental_saver.py:1) 和 [`output_manager/progress_saver.py`](output_manager/progress_saver.py:1) 实现，并依赖于 [`output_manager/ini_writer.py`](output_manager/ini_writer.py:1) 进行实际的文件写入，同时 [`error_handler/error_manager.py`](error_handler/error_manager.py:1) 负责处理失败情况。
A、增量保存
B、进度保存
C、错误处理
D、文件写入
正确答案：ABCD
题目难度：简单
答案解析：自动保存功能旨在防止数据丢失，并在处理过程中提供进度管理和结果输出。主要由 [`output_manager/incremental_saver.py`](output_manager/incremental_saver.py:1) 和 [`output_manager/progress_saver.py`](output_manager/progress_saver.py:1) 实现，并依赖于 [`output_manager/ini_writer.py`](output_manager/ini_writer.py:1) 进行实际的文件写入，同时 [`error_handler/error_manager.py`](error_handler/error_manager.py:1) 负责处理失败情况。
知识点：自动保存|增量保存|进度保存|错误处理


3.【判断题】题目生成的核心逻辑由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调。
正确答案：对
题目难度：简单
答案解析：题目生成的核心逻辑由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调。
知识点：题目生成|核心逻辑
4.【填空题】题目生成的核心逻辑由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调，并依赖于 [`llm_service/openai_client.py`](llm_service/openai_client.py:1) 和 [`utils/question_calculator.py`](utils/question_calculator.py:1) 完成具体任务。题目生成的核心逻辑由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调，并依赖于 [`llm_service/openai_client.py`](llm_service/openai_client.py:1) 和 [`utils/question_calculator.py`](utils/question_calculator.py:1) 完成具体任务。
题目难度：简单
作答上传图片：否
答案解析：题目生成的核心逻辑由 [`quiz_processor/document_to_quiz_processor.py`](quiz_processor/document_to_quiz_processor.py:1) 协调，并依赖于 [`llm_service/openai_client.py`](llm_service/openai_client.py:1) 和 [`utils/question_calculator.py`](utils/question_calculator.py:1) 完成具体任务。
知识点：题目生成|核心逻辑|LLM交互|题目数量计算



5.【简答题】自动保存功能旨在防止数据丢失，并在处理过程中提供进度管理和结果输出。主要由 [`output_manager/incremental_saver.py`](output_manager/incremental_saver.py:1) 和 [`output_manager/progress_saver.py`](output_manager/progress_saver.py:1) 实现，并依赖于 [`output_manager/ini_writer.py`](output_manager/ini_writer.py:1) 进行实际的文件写入，同时 [`error_handler/error_manager.py`](error_handler/error_manager.py:1) 负责处理失败情况。
关键字1：增量保存/进度保存/错误处理
关键字2：文件写入/数据丢失/进度管理
关键字3：结果输出/处理过程/自动保存
题目难度：简单
作答上传图片：否
答案解析：自动保存功能旨在防止数据丢失，并在处理过程中提供进度管理和结果输出。主要由 [`output_manager/incremental_saver.py`](output_manager/incremental_saver.py:1) 和 [`output_manager/progress_saver.py`](output_manager/progress_saver.py:1) 实现，并依赖于 [`output_manager/ini_writer.py`](output_manager/ini_writer.py:1) 进行实际的文件写入，同时 [`error_handler/error_manager.py`](error_handler/error_manager.py:1) 负责处理失败情况。
知识点：自动保存|增量保存|进度保存|错误处理

